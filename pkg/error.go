package pkg

import "errors"

var (
	ErrNotFound                 = errors.New("not found")
	ErrDisposed                 = errors.New("disposed")
	ErrDisabled                 = errors.New("disabled")
	ErrStreamExist              = errors.New("stream exist")
	ErrRecordExists             = errors.New("record exists")
	ErrKick                     = errors.New("kick")
	ErrDiscard                  = errors.New("discard")
	ErrPublishMaxCount          = errors.New("publish max count exceeded")
	ErrPublishTimeout           = errors.New("publish timeout")
	ErrPublishIdleTimeout       = errors.New("publish idle timeout")
	ErrPublishDelayCloseTimeout = errors.New("publish delay close timeout")
	ErrPushRemoteURLExist       = errors.New("push remote url exist")
	ErrSubscribeMaxCount        = errors.New("subscribe max count exceeded")
	ErrSubscribeTimeout         = errors.New("subscribe timeout")
	ErrRestart                  = errors.New("restart")
	ErrInterrupt                = errors.New("interrupt")
	ErrUnsupportCodec           = errors.New("unsupport codec")
	ErrMuted                    = errors.New("muted")
	ErrSkip                     = errors.New("skip")
	ErrNoTrack                  = errors.New("no track")
	ErrNoDB                     = errors.New("no db")
	ErrLost                     = errors.New("lost")
	ErrSeek                     = errors.New("seek")
	ErrRecordSamePath           = errors.New("record same path")
	ErrTransformSame            = errors.New("transform same")
	ErrNotListen                = errors.New("not listen")
	ErrInvalidCredentials       = errors.New("invalid credentials")
)
