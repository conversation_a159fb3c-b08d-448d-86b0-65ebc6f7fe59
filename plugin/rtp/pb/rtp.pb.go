// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rtp.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReceivePSRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Port          uint32                 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Udp           bool                   `protobuf:"varint,3,opt,name=udp,proto3" json:"udp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceivePSRequest) Reset() {
	*x = ReceivePSRequest{}
	mi := &file_rtp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceivePSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceivePSRequest) ProtoMessage() {}

func (x *ReceivePSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rtp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceivePSRequest.ProtoReflect.Descriptor instead.
func (*ReceivePSRequest) Descriptor() ([]byte, []int) {
	return file_rtp_proto_rawDescGZIP(), []int{0}
}

func (x *ReceivePSRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReceivePSRequest) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ReceivePSRequest) GetUdp() bool {
	if x != nil {
		return x.Udp
	}
	return false
}

type ReceivePSResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          int32                  `protobuf:"varint,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceivePSResponse) Reset() {
	*x = ReceivePSResponse{}
	mi := &file_rtp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceivePSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceivePSResponse) ProtoMessage() {}

func (x *ReceivePSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rtp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceivePSResponse.ProtoReflect.Descriptor instead.
func (*ReceivePSResponse) Descriptor() ([]byte, []int) {
	return file_rtp_proto_rawDescGZIP(), []int{1}
}

func (x *ReceivePSResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ReceivePSResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ReceivePSResponse) GetData() int32 {
	if x != nil {
		return x.Data
	}
	return 0
}

type SendPSRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Ip            string                 `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	Port          uint32                 `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	Udp           bool                   `protobuf:"varint,4,opt,name=udp,proto3" json:"udp,omitempty"`
	Ssrc          uint32                 `protobuf:"varint,5,opt,name=ssrc,proto3" json:"ssrc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPSRequest) Reset() {
	*x = SendPSRequest{}
	mi := &file_rtp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPSRequest) ProtoMessage() {}

func (x *SendPSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rtp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPSRequest.ProtoReflect.Descriptor instead.
func (*SendPSRequest) Descriptor() ([]byte, []int) {
	return file_rtp_proto_rawDescGZIP(), []int{2}
}

func (x *SendPSRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *SendPSRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SendPSRequest) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *SendPSRequest) GetUdp() bool {
	if x != nil {
		return x.Udp
	}
	return false
}

func (x *SendPSRequest) GetSsrc() uint32 {
	if x != nil {
		return x.Ssrc
	}
	return 0
}

type SendPSResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          int32                  `protobuf:"varint,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPSResponse) Reset() {
	*x = SendPSResponse{}
	mi := &file_rtp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPSResponse) ProtoMessage() {}

func (x *SendPSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rtp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPSResponse.ProtoReflect.Descriptor instead.
func (*SendPSResponse) Descriptor() ([]byte, []int) {
	return file_rtp_proto_rawDescGZIP(), []int{3}
}

func (x *SendPSResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendPSResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SendPSResponse) GetData() int32 {
	if x != nil {
		return x.Data
	}
	return 0
}

type Peer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Port          uint32                 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Ssrc          uint32                 `protobuf:"varint,3,opt,name=ssrc,proto3" json:"ssrc,omitempty"`
	Mode          string                 `protobuf:"bytes,4,opt,name=mode,proto3" json:"mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Peer) Reset() {
	*x = Peer{}
	mi := &file_rtp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Peer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Peer) ProtoMessage() {}

func (x *Peer) ProtoReflect() protoreflect.Message {
	mi := &file_rtp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Peer.ProtoReflect.Descriptor instead.
func (*Peer) Descriptor() ([]byte, []int) {
	return file_rtp_proto_rawDescGZIP(), []int{4}
}

func (x *Peer) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Peer) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Peer) GetSsrc() uint32 {
	if x != nil {
		return x.Ssrc
	}
	return 0
}

func (x *Peer) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

type ForwardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *Peer                  `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Target        *Peer                  `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForwardRequest) Reset() {
	*x = ForwardRequest{}
	mi := &file_rtp_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardRequest) ProtoMessage() {}

func (x *ForwardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rtp_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardRequest.ProtoReflect.Descriptor instead.
func (*ForwardRequest) Descriptor() ([]byte, []int) {
	return file_rtp_proto_rawDescGZIP(), []int{5}
}

func (x *ForwardRequest) GetSource() *Peer {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *ForwardRequest) GetTarget() *Peer {
	if x != nil {
		return x.Target
	}
	return nil
}

type ForwardResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForwardResponse) Reset() {
	*x = ForwardResponse{}
	mi := &file_rtp_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardResponse) ProtoMessage() {}

func (x *ForwardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rtp_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardResponse.ProtoReflect.Descriptor instead.
func (*ForwardResponse) Descriptor() ([]byte, []int) {
	return file_rtp_proto_rawDescGZIP(), []int{6}
}

func (x *ForwardResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ForwardResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ForwardResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_rtp_proto protoreflect.FileDescriptor

const file_rtp_proto_rawDesc = "" +
	"\n" +
	"\trtp.proto\x12\x03rtp\x1a\x1cgoogle/api/annotations.proto\"X\n" +
	"\x10ReceivePSRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x12\n" +
	"\x04port\x18\x02 \x01(\rR\x04port\x12\x10\n" +
	"\x03udp\x18\x03 \x01(\bR\x03udp\"U\n" +
	"\x11ReceivePSResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\x05R\x04data\"y\n" +
	"\rSendPSRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x0e\n" +
	"\x02ip\x18\x02 \x01(\tR\x02ip\x12\x12\n" +
	"\x04port\x18\x03 \x01(\rR\x04port\x12\x10\n" +
	"\x03udp\x18\x04 \x01(\bR\x03udp\x12\x12\n" +
	"\x04ssrc\x18\x05 \x01(\rR\x04ssrc\"R\n" +
	"\x0eSendPSResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\x05R\x04data\"R\n" +
	"\x04Peer\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\tR\x02ip\x12\x12\n" +
	"\x04port\x18\x02 \x01(\rR\x04port\x12\x12\n" +
	"\x04ssrc\x18\x03 \x01(\rR\x04ssrc\x12\x12\n" +
	"\x04mode\x18\x04 \x01(\tR\x04mode\"V\n" +
	"\x0eForwardRequest\x12!\n" +
	"\x06source\x18\x01 \x01(\v2\t.rtp.PeerR\x06source\x12!\n" +
	"\x06target\x18\x02 \x01(\v2\t.rtp.PeerR\x06target\"Y\n" +
	"\x0fForwardResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess2\xf8\x01\n" +
	"\x03api\x12V\n" +
	"\tReceivePS\x12\x15.rtp.ReceivePSRequest\x1a\x16.rtp.ReceivePSResponse\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/rtp/receive/ps\x12J\n" +
	"\x06SendPS\x12\x12.rtp.SendPSRequest\x1a\x13.rtp.SendPSResponse\"\x17\x82\xd3\xe4\x93\x02\x11:\x01*\"\f/rtp/send/ps\x12M\n" +
	"\aForward\x12\x13.rtp.ForwardRequest\x1a\x14.rtp.ForwardResponse\"\x17\x82\xd3\xe4\x93\x02\x11:\x01*\"\f/rtp/forwardB\x1bZ\x19m7s.live/v5/plugin/rtp/pbb\x06proto3"

var (
	file_rtp_proto_rawDescOnce sync.Once
	file_rtp_proto_rawDescData []byte
)

func file_rtp_proto_rawDescGZIP() []byte {
	file_rtp_proto_rawDescOnce.Do(func() {
		file_rtp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rtp_proto_rawDesc), len(file_rtp_proto_rawDesc)))
	})
	return file_rtp_proto_rawDescData
}

var file_rtp_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_rtp_proto_goTypes = []any{
	(*ReceivePSRequest)(nil),  // 0: rtp.ReceivePSRequest
	(*ReceivePSResponse)(nil), // 1: rtp.ReceivePSResponse
	(*SendPSRequest)(nil),     // 2: rtp.SendPSRequest
	(*SendPSResponse)(nil),    // 3: rtp.SendPSResponse
	(*Peer)(nil),              // 4: rtp.Peer
	(*ForwardRequest)(nil),    // 5: rtp.ForwardRequest
	(*ForwardResponse)(nil),   // 6: rtp.ForwardResponse
}
var file_rtp_proto_depIdxs = []int32{
	4, // 0: rtp.ForwardRequest.source:type_name -> rtp.Peer
	4, // 1: rtp.ForwardRequest.target:type_name -> rtp.Peer
	0, // 2: rtp.api.ReceivePS:input_type -> rtp.ReceivePSRequest
	2, // 3: rtp.api.SendPS:input_type -> rtp.SendPSRequest
	5, // 4: rtp.api.Forward:input_type -> rtp.ForwardRequest
	1, // 5: rtp.api.ReceivePS:output_type -> rtp.ReceivePSResponse
	3, // 6: rtp.api.SendPS:output_type -> rtp.SendPSResponse
	6, // 7: rtp.api.Forward:output_type -> rtp.ForwardResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_rtp_proto_init() }
func file_rtp_proto_init() {
	if File_rtp_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rtp_proto_rawDesc), len(file_rtp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rtp_proto_goTypes,
		DependencyIndexes: file_rtp_proto_depIdxs,
		MessageInfos:      file_rtp_proto_msgTypes,
	}.Build()
	File_rtp_proto = out.File
	file_rtp_proto_goTypes = nil
	file_rtp_proto_depIdxs = nil
}
