// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: test.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	pb "m7s.live/v5/pb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListFilesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []string               `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFilesResponse) Reset() {
	*x = ListFilesResponse{}
	mi := &file_test_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFilesResponse) ProtoMessage() {}

func (x *ListFilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFilesResponse.ProtoReflect.Descriptor instead.
func (*ListFilesResponse) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{0}
}

func (x *ListFilesResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListFilesResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListFilesResponse) GetData() []string {
	if x != nil {
		return x.Data
	}
	return nil
}

// 测试用例相关消息
type TestCase struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	Tasks         []*TestTask            `protobuf:"bytes,4,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Duration      int32                  `protobuf:"varint,8,opt,name=duration,proto3" json:"duration,omitempty"`
	VideoCodec    string                 `protobuf:"bytes,9,opt,name=videoCodec,proto3" json:"videoCodec,omitempty"`
	AudioCodec    string                 `protobuf:"bytes,10,opt,name=audioCodec,proto3" json:"audioCodec,omitempty"`
	VideoOnly     bool                   `protobuf:"varint,11,opt,name=videoOnly,proto3" json:"videoOnly,omitempty"`
	AudioOnly     bool                   `protobuf:"varint,12,opt,name=audioOnly,proto3" json:"audioOnly,omitempty"`
	ErrorMsg      string                 `protobuf:"bytes,13,opt,name=errorMsg,proto3" json:"errorMsg,omitempty"`
	Logs          string                 `protobuf:"bytes,14,opt,name=logs,proto3" json:"logs,omitempty"`
	Tags          []string               `protobuf:"bytes,15,rep,name=tags,proto3" json:"tags,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestCase) Reset() {
	*x = TestCase{}
	mi := &file_test_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestCase) ProtoMessage() {}

func (x *TestCase) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestCase.ProtoReflect.Descriptor instead.
func (*TestCase) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{1}
}

func (x *TestCase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TestCase) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TestCase) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *TestCase) GetTasks() []*TestTask {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *TestCase) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TestCase) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *TestCase) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *TestCase) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *TestCase) GetVideoCodec() string {
	if x != nil {
		return x.VideoCodec
	}
	return ""
}

func (x *TestCase) GetAudioCodec() string {
	if x != nil {
		return x.AudioCodec
	}
	return ""
}

func (x *TestCase) GetVideoOnly() bool {
	if x != nil {
		return x.VideoOnly
	}
	return false
}

func (x *TestCase) GetAudioOnly() bool {
	if x != nil {
		return x.AudioOnly
	}
	return false
}

func (x *TestCase) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *TestCase) GetLogs() string {
	if x != nil {
		return x.Logs
	}
	return ""
}

func (x *TestCase) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type TestTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Action        string                 `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	Delay         *durationpb.Duration   `protobuf:"bytes,2,opt,name=delay,proto3" json:"delay,omitempty"`
	Format        string                 `protobuf:"bytes,3,opt,name=format,proto3" json:"format,omitempty"`
	ServerAddr    string                 `protobuf:"bytes,4,opt,name=serverAddr,proto3" json:"serverAddr,omitempty"`
	VideoFile     string                 `protobuf:"bytes,5,opt,name=videoFile,proto3" json:"videoFile,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestTask) Reset() {
	*x = TestTask{}
	mi := &file_test_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestTask) ProtoMessage() {}

func (x *TestTask) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestTask.ProtoReflect.Descriptor instead.
func (*TestTask) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{2}
}

func (x *TestTask) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *TestTask) GetDelay() *durationpb.Duration {
	if x != nil {
		return x.Delay
	}
	return nil
}

func (x *TestTask) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *TestTask) GetServerAddr() string {
	if x != nil {
		return x.ServerAddr
	}
	return ""
}

func (x *TestTask) GetVideoFile() string {
	if x != nil {
		return x.VideoFile
	}
	return ""
}

type TestCaseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *TestCase              `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestCaseResponse) Reset() {
	*x = TestCaseResponse{}
	mi := &file_test_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestCaseResponse) ProtoMessage() {}

func (x *TestCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestCaseResponse.ProtoReflect.Descriptor instead.
func (*TestCaseResponse) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{3}
}

func (x *TestCaseResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TestCaseResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TestCaseResponse) GetData() *TestCase {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListTestCasesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tags          []string               `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestCasesRequest) Reset() {
	*x = ListTestCasesRequest{}
	mi := &file_test_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestCasesRequest) ProtoMessage() {}

func (x *ListTestCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestCasesRequest.ProtoReflect.Descriptor instead.
func (*ListTestCasesRequest) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{4}
}

func (x *ListTestCasesRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ListTestCasesRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ListTestCasesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*TestCase            `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestCasesResponse) Reset() {
	*x = ListTestCasesResponse{}
	mi := &file_test_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestCasesResponse) ProtoMessage() {}

func (x *ListTestCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestCasesResponse.ProtoReflect.Descriptor instead.
func (*ListTestCasesResponse) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{5}
}

func (x *ListTestCasesResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListTestCasesResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListTestCasesResponse) GetData() []*TestCase {
	if x != nil {
		return x.Data
	}
	return nil
}

type ExecuteTestCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Names         []string               `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteTestCaseRequest) Reset() {
	*x = ExecuteTestCaseRequest{}
	mi := &file_test_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteTestCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteTestCaseRequest) ProtoMessage() {}

func (x *ExecuteTestCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteTestCaseRequest.ProtoReflect.Descriptor instead.
func (*ExecuteTestCaseRequest) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{6}
}

func (x *ExecuteTestCaseRequest) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

// Stress 测试相关消息
type CountResponseData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PushCount     uint32                 `protobuf:"varint,1,opt,name=pushCount,proto3" json:"pushCount,omitempty"`
	PullCount     uint32                 `protobuf:"varint,2,opt,name=pullCount,proto3" json:"pullCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountResponseData) Reset() {
	*x = CountResponseData{}
	mi := &file_test_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountResponseData) ProtoMessage() {}

func (x *CountResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountResponseData.ProtoReflect.Descriptor instead.
func (*CountResponseData) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{7}
}

func (x *CountResponseData) GetPushCount() uint32 {
	if x != nil {
		return x.PushCount
	}
	return 0
}

func (x *CountResponseData) GetPullCount() uint32 {
	if x != nil {
		return x.PullCount
	}
	return 0
}

type CountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *CountResponseData     `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountResponse) Reset() {
	*x = CountResponse{}
	mi := &file_test_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountResponse) ProtoMessage() {}

func (x *CountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountResponse.ProtoReflect.Descriptor instead.
func (*CountResponse) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{8}
}

func (x *CountResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CountResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CountResponse) GetData() *CountResponseData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PushRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Protocol      string                 `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol,omitempty"`
	RemoteURL     string                 `protobuf:"bytes,3,opt,name=remoteURL,proto3" json:"remoteURL,omitempty"`
	PushCount     int32                  `protobuf:"varint,4,opt,name=pushCount,proto3" json:"pushCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushRequest) Reset() {
	*x = PushRequest{}
	mi := &file_test_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushRequest) ProtoMessage() {}

func (x *PushRequest) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushRequest.ProtoReflect.Descriptor instead.
func (*PushRequest) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{9}
}

func (x *PushRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *PushRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *PushRequest) GetRemoteURL() string {
	if x != nil {
		return x.RemoteURL
	}
	return ""
}

func (x *PushRequest) GetPushCount() int32 {
	if x != nil {
		return x.PushCount
	}
	return 0
}

type PullRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RemoteURL     string                 `protobuf:"bytes,1,opt,name=remoteURL,proto3" json:"remoteURL,omitempty"`
	Protocol      string                 `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol,omitempty"`
	PullCount     int32                  `protobuf:"varint,3,opt,name=pullCount,proto3" json:"pullCount,omitempty"`
	TestMode      int32                  `protobuf:"varint,4,opt,name=testMode,proto3" json:"testMode,omitempty"` // 0: pull, 1: pull without publish
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PullRequest) Reset() {
	*x = PullRequest{}
	mi := &file_test_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PullRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullRequest) ProtoMessage() {}

func (x *PullRequest) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullRequest.ProtoReflect.Descriptor instead.
func (*PullRequest) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{10}
}

func (x *PullRequest) GetRemoteURL() string {
	if x != nil {
		return x.RemoteURL
	}
	return ""
}

func (x *PullRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *PullRequest) GetPullCount() int32 {
	if x != nil {
		return x.PullCount
	}
	return 0
}

func (x *PullRequest) GetTestMode() int32 {
	if x != nil {
		return x.TestMode
	}
	return 0
}

var File_test_proto protoreflect.FileDescriptor

const file_test_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"test.proto\x12\x04test\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\fglobal.proto\"U\n" +
	"\x11ListFilesResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x03(\tR\x04data\"\xff\x03\n" +
	"\bTestCase\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x12$\n" +
	"\x05tasks\x18\x04 \x03(\v2\x0e.test.TestTaskR\x05tasks\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x128\n" +
	"\tstartTime\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x124\n" +
	"\aendTime\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x1a\n" +
	"\bduration\x18\b \x01(\x05R\bduration\x12\x1e\n" +
	"\n" +
	"videoCodec\x18\t \x01(\tR\n" +
	"videoCodec\x12\x1e\n" +
	"\n" +
	"audioCodec\x18\n" +
	" \x01(\tR\n" +
	"audioCodec\x12\x1c\n" +
	"\tvideoOnly\x18\v \x01(\bR\tvideoOnly\x12\x1c\n" +
	"\taudioOnly\x18\f \x01(\bR\taudioOnly\x12\x1a\n" +
	"\berrorMsg\x18\r \x01(\tR\berrorMsg\x12\x12\n" +
	"\x04logs\x18\x0e \x01(\tR\x04logs\x12\x12\n" +
	"\x04tags\x18\x0f \x03(\tR\x04tags\"\xa9\x01\n" +
	"\bTestTask\x12\x16\n" +
	"\x06action\x18\x01 \x01(\tR\x06action\x12/\n" +
	"\x05delay\x18\x02 \x01(\v2\x19.google.protobuf.DurationR\x05delay\x12\x16\n" +
	"\x06format\x18\x03 \x01(\tR\x06format\x12\x1e\n" +
	"\n" +
	"serverAddr\x18\x04 \x01(\tR\n" +
	"serverAddr\x12\x1c\n" +
	"\tvideoFile\x18\x05 \x01(\tR\tvideoFile\"d\n" +
	"\x10TestCaseResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04data\x18\x03 \x01(\v2\x0e.test.TestCaseR\x04data\"B\n" +
	"\x14ListTestCasesRequest\x12\x12\n" +
	"\x04tags\x18\x01 \x03(\tR\x04tags\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\"i\n" +
	"\x15ListTestCasesResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04data\x18\x03 \x03(\v2\x0e.test.TestCaseR\x04data\".\n" +
	"\x16ExecuteTestCaseRequest\x12\x14\n" +
	"\x05names\x18\x01 \x03(\tR\x05names\"O\n" +
	"\x11CountResponseData\x12\x1c\n" +
	"\tpushCount\x18\x01 \x01(\rR\tpushCount\x12\x1c\n" +
	"\tpullCount\x18\x02 \x01(\rR\tpullCount\"j\n" +
	"\rCountResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12+\n" +
	"\x04data\x18\x03 \x01(\v2\x17.test.CountResponseDataR\x04data\"\x85\x01\n" +
	"\vPushRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x1a\n" +
	"\bprotocol\x18\x02 \x01(\tR\bprotocol\x12\x1c\n" +
	"\tremoteURL\x18\x03 \x01(\tR\tremoteURL\x12\x1c\n" +
	"\tpushCount\x18\x04 \x01(\x05R\tpushCount\"\x81\x01\n" +
	"\vPullRequest\x12\x1c\n" +
	"\tremoteURL\x18\x01 \x01(\tR\tremoteURL\x12\x1a\n" +
	"\bprotocol\x18\x02 \x01(\tR\bprotocol\x12\x1c\n" +
	"\tpullCount\x18\x03 \x01(\x05R\tpullCount\x12\x1a\n" +
	"\btestMode\x18\x04 \x01(\x05R\btestMode2\xd5\x05\n" +
	"\x03api\x12a\n" +
	"\rListTestCases\x12\x1a.test.ListTestCasesRequest\x1a\x1b.test.ListTestCasesResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/test/api/cases\x12l\n" +
	"\x0fExecuteTestCase\x12\x1c.test.ExecuteTestCaseRequest\x1a\x17.global.SuccessResponse\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/test/api/cases/execute\x12p\n" +
	"\tStartPush\x12\x11.test.PushRequest\x1a\x17.global.SuccessResponse\"7\x82\xd3\xe4\x93\x021:\x01*\",/test/api/stress/push/{protocol}/{pushCount}\x12p\n" +
	"\tStartPull\x12\x11.test.PullRequest\x1a\x17.global.SuccessResponse\"7\x82\xd3\xe4\x93\x021:\x01*\",/test/api/stress/pull/{protocol}/{pullCount}\x12W\n" +
	"\bGetCount\x12\x16.google.protobuf.Empty\x1a\x13.test.CountResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/test/api/stress/count\x12_\n" +
	"\bStopPush\x12\x16.google.protobuf.Empty\x1a\x17.global.SuccessResponse\"\"\x82\xd3\xe4\x93\x02\x1c\"\x1a/test/api/stress/stop/push\x12_\n" +
	"\bStopPull\x12\x16.google.protobuf.Empty\x1a\x17.global.SuccessResponse\"\"\x82\xd3\xe4\x93\x02\x1c\"\x1a/test/api/stress/stop/pullB\x1cZ\x1am7s.live/v5/plugin/test/pbb\x06proto3"

var (
	file_test_proto_rawDescOnce sync.Once
	file_test_proto_rawDescData []byte
)

func file_test_proto_rawDescGZIP() []byte {
	file_test_proto_rawDescOnce.Do(func() {
		file_test_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_test_proto_rawDesc), len(file_test_proto_rawDesc)))
	})
	return file_test_proto_rawDescData
}

var file_test_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_test_proto_goTypes = []any{
	(*ListFilesResponse)(nil),      // 0: test.ListFilesResponse
	(*TestCase)(nil),               // 1: test.TestCase
	(*TestTask)(nil),               // 2: test.TestTask
	(*TestCaseResponse)(nil),       // 3: test.TestCaseResponse
	(*ListTestCasesRequest)(nil),   // 4: test.ListTestCasesRequest
	(*ListTestCasesResponse)(nil),  // 5: test.ListTestCasesResponse
	(*ExecuteTestCaseRequest)(nil), // 6: test.ExecuteTestCaseRequest
	(*CountResponseData)(nil),      // 7: test.CountResponseData
	(*CountResponse)(nil),          // 8: test.CountResponse
	(*PushRequest)(nil),            // 9: test.PushRequest
	(*PullRequest)(nil),            // 10: test.PullRequest
	(*durationpb.Duration)(nil),    // 11: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),  // 12: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),          // 13: google.protobuf.Empty
	(*pb.SuccessResponse)(nil),     // 14: global.SuccessResponse
}
var file_test_proto_depIdxs = []int32{
	11, // 0: test.TestCase.timeout:type_name -> google.protobuf.Duration
	2,  // 1: test.TestCase.tasks:type_name -> test.TestTask
	12, // 2: test.TestCase.startTime:type_name -> google.protobuf.Timestamp
	12, // 3: test.TestCase.endTime:type_name -> google.protobuf.Timestamp
	11, // 4: test.TestTask.delay:type_name -> google.protobuf.Duration
	1,  // 5: test.TestCaseResponse.data:type_name -> test.TestCase
	1,  // 6: test.ListTestCasesResponse.data:type_name -> test.TestCase
	7,  // 7: test.CountResponse.data:type_name -> test.CountResponseData
	4,  // 8: test.api.ListTestCases:input_type -> test.ListTestCasesRequest
	6,  // 9: test.api.ExecuteTestCase:input_type -> test.ExecuteTestCaseRequest
	9,  // 10: test.api.StartPush:input_type -> test.PushRequest
	10, // 11: test.api.StartPull:input_type -> test.PullRequest
	13, // 12: test.api.GetCount:input_type -> google.protobuf.Empty
	13, // 13: test.api.StopPush:input_type -> google.protobuf.Empty
	13, // 14: test.api.StopPull:input_type -> google.protobuf.Empty
	5,  // 15: test.api.ListTestCases:output_type -> test.ListTestCasesResponse
	14, // 16: test.api.ExecuteTestCase:output_type -> global.SuccessResponse
	14, // 17: test.api.StartPush:output_type -> global.SuccessResponse
	14, // 18: test.api.StartPull:output_type -> global.SuccessResponse
	8,  // 19: test.api.GetCount:output_type -> test.CountResponse
	14, // 20: test.api.StopPush:output_type -> global.SuccessResponse
	14, // 21: test.api.StopPull:output_type -> global.SuccessResponse
	15, // [15:22] is the sub-list for method output_type
	8,  // [8:15] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_test_proto_init() }
func file_test_proto_init() {
	if File_test_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_test_proto_rawDesc), len(file_test_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_test_proto_goTypes,
		DependencyIndexes: file_test_proto_depIdxs,
		MessageInfos:      file_test_proto_msgTypes,
	}.Build()
	File_test_proto = out.File
	file_test_proto_goTypes = nil
	file_test_proto_depIdxs = nil
}
