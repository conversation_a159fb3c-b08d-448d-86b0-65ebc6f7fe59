// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: debug.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CPU分析请求参数
type CpuRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Refresh       bool                   `protobuf:"varint,1,opt,name=refresh,proto3" json:"refresh,omitempty"`   // 是否刷新数据
	Duration      uint32                 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"` // 分析时间（秒）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CpuRequest) Reset() {
	*x = CpuRequest{}
	mi := &file_debug_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CpuRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CpuRequest) ProtoMessage() {}

func (x *CpuRequest) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CpuRequest.ProtoReflect.Descriptor instead.
func (*CpuRequest) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{0}
}

func (x *CpuRequest) GetRefresh() bool {
	if x != nil {
		return x.Refresh
	}
	return false
}

func (x *CpuRequest) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type HeapObject struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Count         int64                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Size          int64                  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	SizePerc      float64                `protobuf:"fixed64,4,opt,name=sizePerc,proto3" json:"sizePerc,omitempty"`
	Address       string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	Refs          []string               `protobuf:"bytes,6,rep,name=refs,proto3" json:"refs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeapObject) Reset() {
	*x = HeapObject{}
	mi := &file_debug_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeapObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeapObject) ProtoMessage() {}

func (x *HeapObject) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeapObject.ProtoReflect.Descriptor instead.
func (*HeapObject) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{1}
}

func (x *HeapObject) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *HeapObject) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *HeapObject) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *HeapObject) GetSizePerc() float64 {
	if x != nil {
		return x.SizePerc
	}
	return 0
}

func (x *HeapObject) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *HeapObject) GetRefs() []string {
	if x != nil {
		return x.Refs
	}
	return nil
}

type HeapStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Alloc         uint64                 `protobuf:"varint,1,opt,name=alloc,proto3" json:"alloc,omitempty"`
	TotalAlloc    uint64                 `protobuf:"varint,2,opt,name=totalAlloc,proto3" json:"totalAlloc,omitempty"`
	Sys           uint64                 `protobuf:"varint,3,opt,name=sys,proto3" json:"sys,omitempty"`
	NumGC         uint32                 `protobuf:"varint,4,opt,name=numGC,proto3" json:"numGC,omitempty"`
	HeapAlloc     uint64                 `protobuf:"varint,5,opt,name=heapAlloc,proto3" json:"heapAlloc,omitempty"`
	HeapSys       uint64                 `protobuf:"varint,6,opt,name=heapSys,proto3" json:"heapSys,omitempty"`
	HeapIdle      uint64                 `protobuf:"varint,7,opt,name=heapIdle,proto3" json:"heapIdle,omitempty"`
	HeapInuse     uint64                 `protobuf:"varint,8,opt,name=heapInuse,proto3" json:"heapInuse,omitempty"`
	HeapReleased  uint64                 `protobuf:"varint,9,opt,name=heapReleased,proto3" json:"heapReleased,omitempty"`
	HeapObjects   uint64                 `protobuf:"varint,10,opt,name=heapObjects,proto3" json:"heapObjects,omitempty"`
	GcCPUFraction float64                `protobuf:"fixed64,11,opt,name=gcCPUFraction,proto3" json:"gcCPUFraction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeapStats) Reset() {
	*x = HeapStats{}
	mi := &file_debug_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeapStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeapStats) ProtoMessage() {}

func (x *HeapStats) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeapStats.ProtoReflect.Descriptor instead.
func (*HeapStats) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{2}
}

func (x *HeapStats) GetAlloc() uint64 {
	if x != nil {
		return x.Alloc
	}
	return 0
}

func (x *HeapStats) GetTotalAlloc() uint64 {
	if x != nil {
		return x.TotalAlloc
	}
	return 0
}

func (x *HeapStats) GetSys() uint64 {
	if x != nil {
		return x.Sys
	}
	return 0
}

func (x *HeapStats) GetNumGC() uint32 {
	if x != nil {
		return x.NumGC
	}
	return 0
}

func (x *HeapStats) GetHeapAlloc() uint64 {
	if x != nil {
		return x.HeapAlloc
	}
	return 0
}

func (x *HeapStats) GetHeapSys() uint64 {
	if x != nil {
		return x.HeapSys
	}
	return 0
}

func (x *HeapStats) GetHeapIdle() uint64 {
	if x != nil {
		return x.HeapIdle
	}
	return 0
}

func (x *HeapStats) GetHeapInuse() uint64 {
	if x != nil {
		return x.HeapInuse
	}
	return 0
}

func (x *HeapStats) GetHeapReleased() uint64 {
	if x != nil {
		return x.HeapReleased
	}
	return 0
}

func (x *HeapStats) GetHeapObjects() uint64 {
	if x != nil {
		return x.HeapObjects
	}
	return 0
}

func (x *HeapStats) GetGcCPUFraction() float64 {
	if x != nil {
		return x.GcCPUFraction
	}
	return 0
}

type HeapData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *HeapStats             `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	Objects       []*HeapObject          `protobuf:"bytes,2,rep,name=objects,proto3" json:"objects,omitempty"`
	Edges         []*HeapEdge            `protobuf:"bytes,3,rep,name=edges,proto3" json:"edges,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeapData) Reset() {
	*x = HeapData{}
	mi := &file_debug_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeapData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeapData) ProtoMessage() {}

func (x *HeapData) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeapData.ProtoReflect.Descriptor instead.
func (*HeapData) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{3}
}

func (x *HeapData) GetStats() *HeapStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *HeapData) GetObjects() []*HeapObject {
	if x != nil {
		return x.Objects
	}
	return nil
}

func (x *HeapData) GetEdges() []*HeapEdge {
	if x != nil {
		return x.Edges
	}
	return nil
}

type HeapEdge struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To            string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	FieldName     string                 `protobuf:"bytes,3,opt,name=fieldName,proto3" json:"fieldName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeapEdge) Reset() {
	*x = HeapEdge{}
	mi := &file_debug_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeapEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeapEdge) ProtoMessage() {}

func (x *HeapEdge) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeapEdge.ProtoReflect.Descriptor instead.
func (*HeapEdge) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{4}
}

func (x *HeapEdge) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *HeapEdge) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *HeapEdge) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

type HeapResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *HeapData              `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeapResponse) Reset() {
	*x = HeapResponse{}
	mi := &file_debug_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeapResponse) ProtoMessage() {}

func (x *HeapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeapResponse.ProtoReflect.Descriptor instead.
func (*HeapResponse) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{5}
}

func (x *HeapResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HeapResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HeapResponse) GetData() *HeapData {
	if x != nil {
		return x.Data
	}
	return nil
}

type HeapGraphResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeapGraphResponse) Reset() {
	*x = HeapGraphResponse{}
	mi := &file_debug_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeapGraphResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeapGraphResponse) ProtoMessage() {}

func (x *HeapGraphResponse) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeapGraphResponse.ProtoReflect.Descriptor instead.
func (*HeapGraphResponse) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{6}
}

func (x *HeapGraphResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HeapGraphResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HeapGraphResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type CpuGraphResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CpuGraphResponse) Reset() {
	*x = CpuGraphResponse{}
	mi := &file_debug_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CpuGraphResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CpuGraphResponse) ProtoMessage() {}

func (x *CpuGraphResponse) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CpuGraphResponse.ProtoReflect.Descriptor instead.
func (*CpuGraphResponse) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{7}
}

func (x *CpuGraphResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CpuGraphResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CpuGraphResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// CPU 采样响应数据
type CpuResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *CpuData               `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CpuResponse) Reset() {
	*x = CpuResponse{}
	mi := &file_debug_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CpuResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CpuResponse) ProtoMessage() {}

func (x *CpuResponse) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CpuResponse.ProtoReflect.Descriptor instead.
func (*CpuResponse) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{8}
}

func (x *CpuResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CpuResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CpuResponse) GetData() *CpuData {
	if x != nil {
		return x.Data
	}
	return nil
}

// Monitor plugin messages
type SearchTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     uint32                 `protobuf:"varint,1,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTaskRequest) Reset() {
	*x = SearchTaskRequest{}
	mi := &file_debug_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTaskRequest) ProtoMessage() {}

func (x *SearchTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTaskRequest.ProtoReflect.Descriptor instead.
func (*SearchTaskRequest) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{9}
}

func (x *SearchTaskRequest) GetSessionId() uint32 {
	if x != nil {
		return x.SessionId
	}
	return 0
}

type Task struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Owner         string                 `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
	Type          uint32                 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Description   string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	Reason        string                 `protobuf:"bytes,7,opt,name=reason,proto3" json:"reason,omitempty"`
	SessionId     uint32                 `protobuf:"varint,8,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	ParentId      uint32                 `protobuf:"varint,9,opt,name=parentId,proto3" json:"parentId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Task) Reset() {
	*x = Task{}
	mi := &file_debug_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{10}
}

func (x *Task) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Task) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *Task) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Task) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Task) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *Task) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Task) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Task) GetSessionId() uint32 {
	if x != nil {
		return x.SessionId
	}
	return 0
}

func (x *Task) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

type SearchTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*Task                `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTaskResponse) Reset() {
	*x = SearchTaskResponse{}
	mi := &file_debug_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTaskResponse) ProtoMessage() {}

func (x *SearchTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTaskResponse.ProtoReflect.Descriptor instead.
func (*SearchTaskResponse) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{11}
}

func (x *SearchTaskResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SearchTaskResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SearchTaskResponse) GetData() []*Task {
	if x != nil {
		return x.Data
	}
	return nil
}

type Session struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Pid           uint32                 `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"`
	Args          string                 `protobuf:"bytes,3,opt,name=args,proto3" json:"args,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Session) Reset() {
	*x = Session{}
	mi := &file_debug_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Session) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Session) ProtoMessage() {}

func (x *Session) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Session.ProtoReflect.Descriptor instead.
func (*Session) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{12}
}

func (x *Session) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Session) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *Session) GetArgs() string {
	if x != nil {
		return x.Args
	}
	return ""
}

func (x *Session) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Session) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type SessionListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*Session             `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionListResponse) Reset() {
	*x = SessionListResponse{}
	mi := &file_debug_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionListResponse) ProtoMessage() {}

func (x *SessionListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionListResponse.ProtoReflect.Descriptor instead.
func (*SessionListResponse) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{13}
}

func (x *SessionListResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SessionListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SessionListResponse) GetData() []*Session {
	if x != nil {
		return x.Data
	}
	return nil
}

type CpuData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TotalCpuTimeNs     uint64                 `protobuf:"varint,1,opt,name=total_cpu_time_ns,json=totalCpuTimeNs,proto3" json:"total_cpu_time_ns,omitempty"`           // 总 CPU 时间（纳秒）
	SamplingIntervalNs uint64                 `protobuf:"varint,2,opt,name=sampling_interval_ns,json=samplingIntervalNs,proto3" json:"sampling_interval_ns,omitempty"` // 采样间隔（纳秒）
	Functions          []*FunctionProfile     `protobuf:"bytes,3,rep,name=functions,proto3" json:"functions,omitempty"`                                                // 函数调用栈信息
	Goroutines         []*GoroutineProfile    `protobuf:"bytes,4,rep,name=goroutines,proto3" json:"goroutines,omitempty"`                                              // 协程信息
	SystemCalls        []*SystemCall          `protobuf:"bytes,5,rep,name=system_calls,json=systemCalls,proto3" json:"system_calls,omitempty"`                         // 系统调用信息
	RuntimeStats       *RuntimeStats          `protobuf:"bytes,6,opt,name=runtime_stats,json=runtimeStats,proto3" json:"runtime_stats,omitempty"`                      // 运行时统计信息
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CpuData) Reset() {
	*x = CpuData{}
	mi := &file_debug_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CpuData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CpuData) ProtoMessage() {}

func (x *CpuData) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CpuData.ProtoReflect.Descriptor instead.
func (*CpuData) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{14}
}

func (x *CpuData) GetTotalCpuTimeNs() uint64 {
	if x != nil {
		return x.TotalCpuTimeNs
	}
	return 0
}

func (x *CpuData) GetSamplingIntervalNs() uint64 {
	if x != nil {
		return x.SamplingIntervalNs
	}
	return 0
}

func (x *CpuData) GetFunctions() []*FunctionProfile {
	if x != nil {
		return x.Functions
	}
	return nil
}

func (x *CpuData) GetGoroutines() []*GoroutineProfile {
	if x != nil {
		return x.Goroutines
	}
	return nil
}

func (x *CpuData) GetSystemCalls() []*SystemCall {
	if x != nil {
		return x.SystemCalls
	}
	return nil
}

func (x *CpuData) GetRuntimeStats() *RuntimeStats {
	if x != nil {
		return x.RuntimeStats
	}
	return nil
}

// 函数调用栈信息
type FunctionProfile struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	FunctionName    string                 `protobuf:"bytes,1,opt,name=function_name,json=functionName,proto3" json:"function_name,omitempty"`           // 函数名称
	CpuTimeNs       uint64                 `protobuf:"varint,2,opt,name=cpu_time_ns,json=cpuTimeNs,proto3" json:"cpu_time_ns,omitempty"`                 // 函数消耗的 CPU 时间（纳秒）
	InvocationCount uint64                 `protobuf:"varint,3,opt,name=invocation_count,json=invocationCount,proto3" json:"invocation_count,omitempty"` // 函数调用次数
	CallStack       []string               `protobuf:"bytes,4,rep,name=call_stack,json=callStack,proto3" json:"call_stack,omitempty"`                    // 调用栈（从调用者到被调用者）
	IsInlined       bool                   `protobuf:"varint,5,opt,name=is_inlined,json=isInlined,proto3" json:"is_inlined,omitempty"`                   // 是否是内联函数
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *FunctionProfile) Reset() {
	*x = FunctionProfile{}
	mi := &file_debug_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FunctionProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionProfile) ProtoMessage() {}

func (x *FunctionProfile) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionProfile.ProtoReflect.Descriptor instead.
func (*FunctionProfile) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{15}
}

func (x *FunctionProfile) GetFunctionName() string {
	if x != nil {
		return x.FunctionName
	}
	return ""
}

func (x *FunctionProfile) GetCpuTimeNs() uint64 {
	if x != nil {
		return x.CpuTimeNs
	}
	return 0
}

func (x *FunctionProfile) GetInvocationCount() uint64 {
	if x != nil {
		return x.InvocationCount
	}
	return 0
}

func (x *FunctionProfile) GetCallStack() []string {
	if x != nil {
		return x.CallStack
	}
	return nil
}

func (x *FunctionProfile) GetIsInlined() bool {
	if x != nil {
		return x.IsInlined
	}
	return false
}

// 协程信息
type GoroutineProfile struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                  // 协程 ID
	State         string                 `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`                             // 协程状态（如 running, blocked 等）
	CpuTimeNs     uint64                 `protobuf:"varint,3,opt,name=cpu_time_ns,json=cpuTimeNs,proto3" json:"cpu_time_ns,omitempty"` // 协程消耗的 CPU 时间（纳秒）
	CallStack     []string               `protobuf:"bytes,4,rep,name=call_stack,json=callStack,proto3" json:"call_stack,omitempty"`    // 协程的调用栈
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoroutineProfile) Reset() {
	*x = GoroutineProfile{}
	mi := &file_debug_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoroutineProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoroutineProfile) ProtoMessage() {}

func (x *GoroutineProfile) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoroutineProfile.ProtoReflect.Descriptor instead.
func (*GoroutineProfile) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{16}
}

func (x *GoroutineProfile) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GoroutineProfile) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *GoroutineProfile) GetCpuTimeNs() uint64 {
	if x != nil {
		return x.CpuTimeNs
	}
	return 0
}

func (x *GoroutineProfile) GetCallStack() []string {
	if x != nil {
		return x.CallStack
	}
	return nil
}

// 系统调用信息
type SystemCall struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                               // 系统调用名称
	CpuTimeNs     uint64                 `protobuf:"varint,2,opt,name=cpu_time_ns,json=cpuTimeNs,proto3" json:"cpu_time_ns,omitempty"` // 系统调用消耗的 CPU 时间（纳秒）
	Count         uint64                 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`                            // 系统调用次数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SystemCall) Reset() {
	*x = SystemCall{}
	mi := &file_debug_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemCall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemCall) ProtoMessage() {}

func (x *SystemCall) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemCall.ProtoReflect.Descriptor instead.
func (*SystemCall) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{17}
}

func (x *SystemCall) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SystemCall) GetCpuTimeNs() uint64 {
	if x != nil {
		return x.CpuTimeNs
	}
	return 0
}

func (x *SystemCall) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 运行时统计信息
type RuntimeStats struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	GcCpuFraction  float64                `protobuf:"fixed64,1,opt,name=gc_cpu_fraction,json=gcCpuFraction,proto3" json:"gc_cpu_fraction,omitempty"`   // 垃圾回收占用的 CPU 时间比例
	GcCount        uint64                 `protobuf:"varint,2,opt,name=gc_count,json=gcCount,proto3" json:"gc_count,omitempty"`                        // 垃圾回收次数
	GcPauseTimeNs  uint64                 `protobuf:"varint,3,opt,name=gc_pause_time_ns,json=gcPauseTimeNs,proto3" json:"gc_pause_time_ns,omitempty"`  // 垃圾回收暂停时间（纳秒）
	BlockingTimeNs uint64                 `protobuf:"varint,4,opt,name=blocking_time_ns,json=blockingTimeNs,proto3" json:"blocking_time_ns,omitempty"` // 阻塞时间（纳秒）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RuntimeStats) Reset() {
	*x = RuntimeStats{}
	mi := &file_debug_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RuntimeStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuntimeStats) ProtoMessage() {}

func (x *RuntimeStats) ProtoReflect() protoreflect.Message {
	mi := &file_debug_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuntimeStats.ProtoReflect.Descriptor instead.
func (*RuntimeStats) Descriptor() ([]byte, []int) {
	return file_debug_proto_rawDescGZIP(), []int{18}
}

func (x *RuntimeStats) GetGcCpuFraction() float64 {
	if x != nil {
		return x.GcCpuFraction
	}
	return 0
}

func (x *RuntimeStats) GetGcCount() uint64 {
	if x != nil {
		return x.GcCount
	}
	return 0
}

func (x *RuntimeStats) GetGcPauseTimeNs() uint64 {
	if x != nil {
		return x.GcPauseTimeNs
	}
	return 0
}

func (x *RuntimeStats) GetBlockingTimeNs() uint64 {
	if x != nil {
		return x.BlockingTimeNs
	}
	return 0
}

var File_debug_proto protoreflect.FileDescriptor

const file_debug_proto_rawDesc = "" +
	"\n" +
	"\vdebug.proto\x12\x05debug\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"B\n" +
	"\n" +
	"CpuRequest\x12\x18\n" +
	"\arefresh\x18\x01 \x01(\bR\arefresh\x12\x1a\n" +
	"\bduration\x18\x02 \x01(\rR\bduration\"\x94\x01\n" +
	"\n" +
	"HeapObject\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x03R\x05count\x12\x12\n" +
	"\x04size\x18\x03 \x01(\x03R\x04size\x12\x1a\n" +
	"\bsizePerc\x18\x04 \x01(\x01R\bsizePerc\x12\x18\n" +
	"\aaddress\x18\x05 \x01(\tR\aaddress\x12\x12\n" +
	"\x04refs\x18\x06 \x03(\tR\x04refs\"\xc7\x02\n" +
	"\tHeapStats\x12\x14\n" +
	"\x05alloc\x18\x01 \x01(\x04R\x05alloc\x12\x1e\n" +
	"\n" +
	"totalAlloc\x18\x02 \x01(\x04R\n" +
	"totalAlloc\x12\x10\n" +
	"\x03sys\x18\x03 \x01(\x04R\x03sys\x12\x14\n" +
	"\x05numGC\x18\x04 \x01(\rR\x05numGC\x12\x1c\n" +
	"\theapAlloc\x18\x05 \x01(\x04R\theapAlloc\x12\x18\n" +
	"\aheapSys\x18\x06 \x01(\x04R\aheapSys\x12\x1a\n" +
	"\bheapIdle\x18\a \x01(\x04R\bheapIdle\x12\x1c\n" +
	"\theapInuse\x18\b \x01(\x04R\theapInuse\x12\"\n" +
	"\fheapReleased\x18\t \x01(\x04R\fheapReleased\x12 \n" +
	"\vheapObjects\x18\n" +
	" \x01(\x04R\vheapObjects\x12$\n" +
	"\rgcCPUFraction\x18\v \x01(\x01R\rgcCPUFraction\"\x86\x01\n" +
	"\bHeapData\x12&\n" +
	"\x05stats\x18\x01 \x01(\v2\x10.debug.HeapStatsR\x05stats\x12+\n" +
	"\aobjects\x18\x02 \x03(\v2\x11.debug.HeapObjectR\aobjects\x12%\n" +
	"\x05edges\x18\x03 \x03(\v2\x0f.debug.HeapEdgeR\x05edges\"L\n" +
	"\bHeapEdge\x12\x12\n" +
	"\x04from\x18\x01 \x01(\tR\x04from\x12\x0e\n" +
	"\x02to\x18\x02 \x01(\tR\x02to\x12\x1c\n" +
	"\tfieldName\x18\x03 \x01(\tR\tfieldName\"a\n" +
	"\fHeapResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12#\n" +
	"\x04data\x18\x03 \x01(\v2\x0f.debug.HeapDataR\x04data\"U\n" +
	"\x11HeapGraphResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\"T\n" +
	"\x10CpuGraphResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\"_\n" +
	"\vCpuResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04data\x18\x03 \x01(\v2\x0e.debug.CpuDataR\x04data\"1\n" +
	"\x11SearchTaskRequest\x12\x1c\n" +
	"\tsessionId\x18\x01 \x01(\rR\tsessionId\"\xa4\x02\n" +
	"\x04Task\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x14\n" +
	"\x05owner\x18\x02 \x01(\tR\x05owner\x12\x12\n" +
	"\x04type\x18\x03 \x01(\rR\x04type\x128\n" +
	"\tstartTime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x124\n" +
	"\aendTime\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12\x16\n" +
	"\x06reason\x18\a \x01(\tR\x06reason\x12\x1c\n" +
	"\tsessionId\x18\b \x01(\rR\tsessionId\x12\x1a\n" +
	"\bparentId\x18\t \x01(\rR\bparentId\"c\n" +
	"\x12SearchTaskResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1f\n" +
	"\x04data\x18\x03 \x03(\v2\v.debug.TaskR\x04data\"\xaf\x01\n" +
	"\aSession\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x10\n" +
	"\x03pid\x18\x02 \x01(\rR\x03pid\x12\x12\n" +
	"\x04args\x18\x03 \x01(\tR\x04args\x128\n" +
	"\tstartTime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x124\n" +
	"\aendTime\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\"g\n" +
	"\x13SessionListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04data\x18\x03 \x03(\v2\x0e.debug.SessionR\x04data\"\xc5\x02\n" +
	"\aCpuData\x12)\n" +
	"\x11total_cpu_time_ns\x18\x01 \x01(\x04R\x0etotalCpuTimeNs\x120\n" +
	"\x14sampling_interval_ns\x18\x02 \x01(\x04R\x12samplingIntervalNs\x124\n" +
	"\tfunctions\x18\x03 \x03(\v2\x16.debug.FunctionProfileR\tfunctions\x127\n" +
	"\n" +
	"goroutines\x18\x04 \x03(\v2\x17.debug.GoroutineProfileR\n" +
	"goroutines\x124\n" +
	"\fsystem_calls\x18\x05 \x03(\v2\x11.debug.SystemCallR\vsystemCalls\x128\n" +
	"\rruntime_stats\x18\x06 \x01(\v2\x13.debug.RuntimeStatsR\fruntimeStats\"\xbf\x01\n" +
	"\x0fFunctionProfile\x12#\n" +
	"\rfunction_name\x18\x01 \x01(\tR\ffunctionName\x12\x1e\n" +
	"\vcpu_time_ns\x18\x02 \x01(\x04R\tcpuTimeNs\x12)\n" +
	"\x10invocation_count\x18\x03 \x01(\x04R\x0finvocationCount\x12\x1d\n" +
	"\n" +
	"call_stack\x18\x04 \x03(\tR\tcallStack\x12\x1d\n" +
	"\n" +
	"is_inlined\x18\x05 \x01(\bR\tisInlined\"w\n" +
	"\x10GoroutineProfile\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x14\n" +
	"\x05state\x18\x02 \x01(\tR\x05state\x12\x1e\n" +
	"\vcpu_time_ns\x18\x03 \x01(\x04R\tcpuTimeNs\x12\x1d\n" +
	"\n" +
	"call_stack\x18\x04 \x03(\tR\tcallStack\"V\n" +
	"\n" +
	"SystemCall\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1e\n" +
	"\vcpu_time_ns\x18\x02 \x01(\x04R\tcpuTimeNs\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x04R\x05count\"\xa4\x01\n" +
	"\fRuntimeStats\x12&\n" +
	"\x0fgc_cpu_fraction\x18\x01 \x01(\x01R\rgcCpuFraction\x12\x19\n" +
	"\bgc_count\x18\x02 \x01(\x04R\agcCount\x12'\n" +
	"\x10gc_pause_time_ns\x18\x03 \x01(\x04R\rgcPauseTimeNs\x12(\n" +
	"\x10blocking_time_ns\x18\x04 \x01(\x04R\x0eblockingTimeNs2\xa5\x04\n" +
	"\x03api\x12O\n" +
	"\aGetHeap\x12\x16.google.protobuf.Empty\x1a\x13.debug.HeapResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/debug/api/heap\x12_\n" +
	"\fGetHeapGraph\x12\x16.google.protobuf.Empty\x1a\x18.debug.HeapGraphResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/debug/api/heap/graph\x12W\n" +
	"\vGetCpuGraph\x12\x11.debug.CpuRequest\x1a\x17.debug.CpuGraphResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/debug/api/cpu/graph\x12G\n" +
	"\x06GetCpu\x12\x11.debug.CpuRequest\x1a\x12.debug.CpuResponse\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/debug/api/cpu\x12f\n" +
	"\n" +
	"SearchTask\x12\x18.debug.SearchTaskRequest\x1a\x19.debug.SearchTaskResponse\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/debug/api/task/{sessionId}\x12b\n" +
	"\vSessionList\x12\x16.google.protobuf.Empty\x1a\x1a.debug.SessionListResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/debug/api/session/listB\x1dZ\x1bm7s.live/v5/plugin/debug/pbb\x06proto3"

var (
	file_debug_proto_rawDescOnce sync.Once
	file_debug_proto_rawDescData []byte
)

func file_debug_proto_rawDescGZIP() []byte {
	file_debug_proto_rawDescOnce.Do(func() {
		file_debug_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_debug_proto_rawDesc), len(file_debug_proto_rawDesc)))
	})
	return file_debug_proto_rawDescData
}

var file_debug_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_debug_proto_goTypes = []any{
	(*CpuRequest)(nil),            // 0: debug.CpuRequest
	(*HeapObject)(nil),            // 1: debug.HeapObject
	(*HeapStats)(nil),             // 2: debug.HeapStats
	(*HeapData)(nil),              // 3: debug.HeapData
	(*HeapEdge)(nil),              // 4: debug.HeapEdge
	(*HeapResponse)(nil),          // 5: debug.HeapResponse
	(*HeapGraphResponse)(nil),     // 6: debug.HeapGraphResponse
	(*CpuGraphResponse)(nil),      // 7: debug.CpuGraphResponse
	(*CpuResponse)(nil),           // 8: debug.CpuResponse
	(*SearchTaskRequest)(nil),     // 9: debug.SearchTaskRequest
	(*Task)(nil),                  // 10: debug.Task
	(*SearchTaskResponse)(nil),    // 11: debug.SearchTaskResponse
	(*Session)(nil),               // 12: debug.Session
	(*SessionListResponse)(nil),   // 13: debug.SessionListResponse
	(*CpuData)(nil),               // 14: debug.CpuData
	(*FunctionProfile)(nil),       // 15: debug.FunctionProfile
	(*GoroutineProfile)(nil),      // 16: debug.GoroutineProfile
	(*SystemCall)(nil),            // 17: debug.SystemCall
	(*RuntimeStats)(nil),          // 18: debug.RuntimeStats
	(*timestamppb.Timestamp)(nil), // 19: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 20: google.protobuf.Empty
}
var file_debug_proto_depIdxs = []int32{
	2,  // 0: debug.HeapData.stats:type_name -> debug.HeapStats
	1,  // 1: debug.HeapData.objects:type_name -> debug.HeapObject
	4,  // 2: debug.HeapData.edges:type_name -> debug.HeapEdge
	3,  // 3: debug.HeapResponse.data:type_name -> debug.HeapData
	14, // 4: debug.CpuResponse.data:type_name -> debug.CpuData
	19, // 5: debug.Task.startTime:type_name -> google.protobuf.Timestamp
	19, // 6: debug.Task.endTime:type_name -> google.protobuf.Timestamp
	10, // 7: debug.SearchTaskResponse.data:type_name -> debug.Task
	19, // 8: debug.Session.startTime:type_name -> google.protobuf.Timestamp
	19, // 9: debug.Session.endTime:type_name -> google.protobuf.Timestamp
	12, // 10: debug.SessionListResponse.data:type_name -> debug.Session
	15, // 11: debug.CpuData.functions:type_name -> debug.FunctionProfile
	16, // 12: debug.CpuData.goroutines:type_name -> debug.GoroutineProfile
	17, // 13: debug.CpuData.system_calls:type_name -> debug.SystemCall
	18, // 14: debug.CpuData.runtime_stats:type_name -> debug.RuntimeStats
	20, // 15: debug.api.GetHeap:input_type -> google.protobuf.Empty
	20, // 16: debug.api.GetHeapGraph:input_type -> google.protobuf.Empty
	0,  // 17: debug.api.GetCpuGraph:input_type -> debug.CpuRequest
	0,  // 18: debug.api.GetCpu:input_type -> debug.CpuRequest
	9,  // 19: debug.api.SearchTask:input_type -> debug.SearchTaskRequest
	20, // 20: debug.api.SessionList:input_type -> google.protobuf.Empty
	5,  // 21: debug.api.GetHeap:output_type -> debug.HeapResponse
	6,  // 22: debug.api.GetHeapGraph:output_type -> debug.HeapGraphResponse
	7,  // 23: debug.api.GetCpuGraph:output_type -> debug.CpuGraphResponse
	8,  // 24: debug.api.GetCpu:output_type -> debug.CpuResponse
	11, // 25: debug.api.SearchTask:output_type -> debug.SearchTaskResponse
	13, // 26: debug.api.SessionList:output_type -> debug.SessionListResponse
	21, // [21:27] is the sub-list for method output_type
	15, // [15:21] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_debug_proto_init() }
func file_debug_proto_init() {
	if File_debug_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_debug_proto_rawDesc), len(file_debug_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_debug_proto_goTypes,
		DependencyIndexes: file_debug_proto_depIdxs,
		MessageInfos:      file_debug_proto_msgTypes,
	}.Build()
	File_debug_proto = out.File
	file_debug_proto_goTypes = nil
	file_debug_proto_depIdxs = nil
}
