syntax = "proto3";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
package debug;
option go_package="m7s.live/v5/plugin/debug/pb";

service api {
  rpc GetHeap (google.protobuf.Empty) returns (HeapResponse) {
    option (google.api.http) = {
      get: "/debug/api/heap"
    };
  }
  rpc GetHeapGraph (google.protobuf.Empty) returns (HeapGraphResponse) {
    option (google.api.http) = {
      get: "/debug/api/heap/graph"
    };
  }
  rpc GetCpuGraph (CpuRequest) returns (CpuGraphResponse) {
    option (google.api.http) = {
      get: "/debug/api/cpu/graph"
    };
  }
  rpc GetCpu (CpuRequest) returns (CpuResponse) {
    option (google.api.http) = {
      get: "/debug/api/cpu"
    };
  }
  
  rpc SearchTask (SearchTaskRequest) returns (SearchTaskResponse) {
    option (google.api.http) = {
      get: "/debug/api/task/{sessionId}"
    };
  }
  rpc SessionList (google.protobuf.Empty) returns (SessionListResponse) {
    option (google.api.http) = {
      get: "/debug/api/session/list"
    };
  }
}

// CPU分析请求参数
message CpuRequest {
  bool refresh = 1;              // 是否刷新数据
  uint32 duration = 2;           // 分析时间（秒）
}

message HeapObject {
  string type = 1;
  int64 count = 2;
  int64 size = 3;
  double sizePerc = 4;
  string address = 5;
  repeated string refs = 6;
}

message HeapStats {
  uint64 alloc = 1;
  uint64 totalAlloc = 2;
  uint64 sys = 3;
  uint32 numGC = 4;
  uint64 heapAlloc = 5;
  uint64 heapSys = 6;
  uint64 heapIdle = 7;
  uint64 heapInuse = 8;
  uint64 heapReleased = 9;
  uint64 heapObjects = 10;
  double gcCPUFraction = 11;
}

message HeapData {
  HeapStats stats = 1;
  repeated HeapObject objects = 2;
  repeated HeapEdge edges = 3;
}

message HeapEdge {
  string from = 1;
  string to = 2;
  string fieldName = 3;
}

message HeapResponse {
  uint32 code = 1;
  string message = 2;
  HeapData data = 3;
}

message HeapGraphResponse {
  uint32 code = 1;
  string message = 2;
  string data = 3;
}

message CpuGraphResponse {
  uint32 code = 1;
  string message = 2;
  string data = 3;
}

// CPU 采样响应数据
message CpuResponse {
  uint32 code = 1;
  string message = 2;
  CpuData data = 3;
}

// Monitor plugin messages
message SearchTaskRequest {
  uint32 sessionId = 1;
}

message Task {
  uint32 id = 1;
  string owner = 2;
  uint32 type = 3;
  google.protobuf.Timestamp startTime = 4;
  google.protobuf.Timestamp endTime = 5;
  string description = 6;
  string reason = 7;
  uint32 sessionId = 8;
  uint32 parentId = 9;
}

message SearchTaskResponse {
  uint32 code = 1;
  string message = 2;
  repeated Task data = 3;
}

message Session {
  uint32 id = 1;
  uint32 pid = 2;
  string args = 3;
  google.protobuf.Timestamp startTime = 4;
  google.protobuf.Timestamp endTime = 5;
}

message SessionListResponse {
  uint32 code = 1;
  string message = 2;
  repeated Session data = 3;
}

message CpuData {
  uint64 total_cpu_time_ns = 1;           // 总 CPU 时间（纳秒）
  uint64 sampling_interval_ns = 2;        // 采样间隔（纳秒）
  repeated FunctionProfile functions = 3; // 函数调用栈信息
  repeated GoroutineProfile goroutines = 4; // 协程信息
  repeated SystemCall system_calls = 5;     // 系统调用信息
  RuntimeStats runtime_stats = 6;           // 运行时统计信息
}

// 函数调用栈信息
message FunctionProfile {
  string function_name = 1;       // 函数名称
  uint64 cpu_time_ns = 2;         // 函数消耗的 CPU 时间（纳秒）
  uint64 invocation_count = 3;    // 函数调用次数
  repeated string call_stack = 4; // 调用栈（从调用者到被调用者）
  bool is_inlined = 5;            // 是否是内联函数
}
// 协程信息
message GoroutineProfile {
  uint64 id = 1;                  // 协程 ID
  string state = 2;               // 协程状态（如 running, blocked 等）
  uint64 cpu_time_ns = 3;         // 协程消耗的 CPU 时间（纳秒）
  repeated string call_stack = 4; // 协程的调用栈
}

// 系统调用信息
message SystemCall {
  string name = 1;                // 系统调用名称
  uint64 cpu_time_ns = 2;         // 系统调用消耗的 CPU 时间（纳秒）
  uint64 count = 3;               // 系统调用次数
}

// 运行时统计信息
message RuntimeStats {
  double gc_cpu_fraction = 1;     // 垃圾回收占用的 CPU 时间比例
  uint64 gc_count = 2;            // 垃圾回收次数
  uint64 gc_pause_time_ns = 3;    // 垃圾回收暂停时间（纳秒）
  uint64 blocking_time_ns = 4;    // 阻塞时间（纳秒）
}
