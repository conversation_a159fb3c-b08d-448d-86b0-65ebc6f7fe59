// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: gb28181.proto

//import "global.proto";

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Api_List_FullMethodName                              = "/gb28181pro.api/List"
	Api_GetDevice_FullMethodName                         = "/gb28181pro.api/GetDevice"
	Api_GetDevices_FullMethodName                        = "/gb28181pro.api/GetDevices"
	Api_GetChannels_FullMethodName                       = "/gb28181pro.api/GetChannels"
	Api_SyncDevice_FullMethodName                        = "/gb28181pro.api/SyncDevice"
	Api_DeleteDevice_FullMethodName                      = "/gb28181pro.api/DeleteDevice"
	Api_GetSubChannels_FullMethodName                    = "/gb28181pro.api/GetSubChannels"
	Api_ChangeAudio_FullMethodName                       = "/gb28181pro.api/ChangeAudio"
	Api_UpdateChannelStreamIdentification_FullMethodName = "/gb28181pro.api/UpdateChannelStreamIdentification"
	Api_UpdateTransport_FullMethodName                   = "/gb28181pro.api/UpdateTransport"
	Api_AddDevice_FullMethodName                         = "/gb28181pro.api/AddDevice"
	Api_UpdateDevice_FullMethodName                      = "/gb28181pro.api/UpdateDevice"
	Api_GetDeviceStatus_FullMethodName                   = "/gb28181pro.api/GetDeviceStatus"
	Api_GetDeviceAlarm_FullMethodName                    = "/gb28181pro.api/GetDeviceAlarm"
	Api_GetSyncStatus_FullMethodName                     = "/gb28181pro.api/GetSyncStatus"
	Api_GetSubscribeInfo_FullMethodName                  = "/gb28181pro.api/GetSubscribeInfo"
	Api_GetSnap_FullMethodName                           = "/gb28181pro.api/GetSnap"
	Api_StopConvert_FullMethodName                       = "/gb28181pro.api/StopConvert"
	Api_StartBroadcast_FullMethodName                    = "/gb28181pro.api/StartBroadcast"
	Api_StopBroadcast_FullMethodName                     = "/gb28181pro.api/StopBroadcast"
	Api_GetAllSSRC_FullMethodName                        = "/gb28181pro.api/GetAllSSRC"
	Api_GetRawChannel_FullMethodName                     = "/gb28181pro.api/GetRawChannel"
	Api_AddPlatform_FullMethodName                       = "/gb28181pro.api/AddPlatform"
	Api_GetPlatform_FullMethodName                       = "/gb28181pro.api/GetPlatform"
	Api_UpdatePlatform_FullMethodName                    = "/gb28181pro.api/UpdatePlatform"
	Api_DeletePlatform_FullMethodName                    = "/gb28181pro.api/DeletePlatform"
	Api_ListPlatforms_FullMethodName                     = "/gb28181pro.api/ListPlatforms"
	Api_QueryRecord_FullMethodName                       = "/gb28181pro.api/QueryRecord"
	Api_PtzControl_FullMethodName                        = "/gb28181pro.api/PtzControl"
	Api_IrisControl_FullMethodName                       = "/gb28181pro.api/IrisControl"
	Api_FocusControl_FullMethodName                      = "/gb28181pro.api/FocusControl"
	Api_QueryPreset_FullMethodName                       = "/gb28181pro.api/QueryPreset"
	Api_AddPreset_FullMethodName                         = "/gb28181pro.api/AddPreset"
	Api_CallPreset_FullMethodName                        = "/gb28181pro.api/CallPreset"
	Api_DeletePreset_FullMethodName                      = "/gb28181pro.api/DeletePreset"
	Api_AddCruisePoint_FullMethodName                    = "/gb28181pro.api/AddCruisePoint"
	Api_DeleteCruisePoint_FullMethodName                 = "/gb28181pro.api/DeleteCruisePoint"
	Api_SetCruiseSpeed_FullMethodName                    = "/gb28181pro.api/SetCruiseSpeed"
	Api_SetCruiseTime_FullMethodName                     = "/gb28181pro.api/SetCruiseTime"
	Api_StartCruise_FullMethodName                       = "/gb28181pro.api/StartCruise"
	Api_StopCruise_FullMethodName                        = "/gb28181pro.api/StopCruise"
	Api_StartScan_FullMethodName                         = "/gb28181pro.api/StartScan"
	Api_StopScan_FullMethodName                          = "/gb28181pro.api/StopScan"
	Api_SetScanLeft_FullMethodName                       = "/gb28181pro.api/SetScanLeft"
	Api_SetScanRight_FullMethodName                      = "/gb28181pro.api/SetScanRight"
	Api_SetScanSpeed_FullMethodName                      = "/gb28181pro.api/SetScanSpeed"
	Api_WiperControl_FullMethodName                      = "/gb28181pro.api/WiperControl"
	Api_AuxiliaryControl_FullMethodName                  = "/gb28181pro.api/AuxiliaryControl"
	Api_TestSip_FullMethodName                           = "/gb28181pro.api/TestSip"
	Api_SearchAlarms_FullMethodName                      = "/gb28181pro.api/SearchAlarms"
	Api_AddPlatformChannel_FullMethodName                = "/gb28181pro.api/AddPlatformChannel"
	Api_Recording_FullMethodName                         = "/gb28181pro.api/Recording"
	Api_UploadJpeg_FullMethodName                        = "/gb28181pro.api/UploadJpeg"
	Api_UpdateChannel_FullMethodName                     = "/gb28181pro.api/UpdateChannel"
	Api_PlaybackPause_FullMethodName                     = "/gb28181pro.api/PlaybackPause"
	Api_PlaybackResume_FullMethodName                    = "/gb28181pro.api/PlaybackResume"
	Api_PlaybackSeek_FullMethodName                      = "/gb28181pro.api/PlaybackSeek"
	Api_PlaybackSpeed_FullMethodName                     = "/gb28181pro.api/PlaybackSpeed"
	Api_GetGroups_FullMethodName                         = "/gb28181pro.api/GetGroups"
	Api_AddGroup_FullMethodName                          = "/gb28181pro.api/AddGroup"
	Api_UpdateGroup_FullMethodName                       = "/gb28181pro.api/UpdateGroup"
	Api_DeleteGroup_FullMethodName                       = "/gb28181pro.api/DeleteGroup"
	Api_AddGroupChannel_FullMethodName                   = "/gb28181pro.api/AddGroupChannel"
	Api_DeleteGroupChannel_FullMethodName                = "/gb28181pro.api/DeleteGroupChannel"
	Api_GetGroupChannels_FullMethodName                  = "/gb28181pro.api/GetGroupChannels"
	Api_RemoveDevice_FullMethodName                      = "/gb28181pro.api/RemoveDevice"
	Api_ReceiveAlarm_FullMethodName                      = "/gb28181pro.api/ReceiveAlarm"
)

// ApiClient is the client API for Api service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApiClient interface {
	// 获取设备列表
	List(ctx context.Context, in *GetDevicesRequest, opts ...grpc.CallOption) (*DevicesPageInfo, error)
	// 使用ID查询国标设备
	GetDevice(ctx context.Context, in *GetDeviceRequest, opts ...grpc.CallOption) (*DeviceResponse, error)
	// 分页查询国标设备
	GetDevices(ctx context.Context, in *GetDevicesRequest, opts ...grpc.CallOption) (*DevicesPageInfo, error)
	// 分页查询通道
	GetChannels(ctx context.Context, in *GetChannelsRequest, opts ...grpc.CallOption) (*ChannelsPageInfo, error)
	// 同步设备通道
	SyncDevice(ctx context.Context, in *SyncDeviceRequest, opts ...grpc.CallOption) (*SyncStatus, error)
	// 移除设备
	DeleteDevice(ctx context.Context, in *DeleteDeviceRequest, opts ...grpc.CallOption) (*DeleteDeviceResponse, error)
	// 分页查询子目录通道
	GetSubChannels(ctx context.Context, in *GetSubChannelsRequest, opts ...grpc.CallOption) (*ChannelsPageInfo, error)
	// 开启/关闭通道的音频
	ChangeAudio(ctx context.Context, in *ChangeAudioRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 修改通道的码流类型
	UpdateChannelStreamIdentification(ctx context.Context, in *Channel, opts ...grpc.CallOption) (*BaseResponse, error)
	// 修改数据流传输模式
	UpdateTransport(ctx context.Context, in *UpdateTransportRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 添加设备信息
	AddDevice(ctx context.Context, in *Device, opts ...grpc.CallOption) (*BaseResponse, error)
	// 更新设备信息
	UpdateDevice(ctx context.Context, in *Device, opts ...grpc.CallOption) (*BaseResponse, error)
	// 设备状态查询
	GetDeviceStatus(ctx context.Context, in *GetDeviceStatusRequest, opts ...grpc.CallOption) (*DeviceStatusResponse, error)
	// 设备报警查询
	GetDeviceAlarm(ctx context.Context, in *GetDeviceAlarmRequest, opts ...grpc.CallOption) (*DeviceAlarmResponse, error)
	// 获取通道同步进度
	GetSyncStatus(ctx context.Context, in *GetSyncStatusRequest, opts ...grpc.CallOption) (*SyncStatus, error)
	// 获取设备的订阅状态
	GetSubscribeInfo(ctx context.Context, in *GetSubscribeInfoRequest, opts ...grpc.CallOption) (*SubscribeInfoResponse, error)
	// 请求截图
	GetSnap(ctx context.Context, in *GetSnapRequest, opts ...grpc.CallOption) (*SnapResponse, error)
	// 结束转码
	StopConvert(ctx context.Context, in *ConvertStopRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 语音广播命令
	StartBroadcast(ctx context.Context, in *BroadcastRequest, opts ...grpc.CallOption) (*BroadcastResponse, error)
	// 停止语音广播
	StopBroadcast(ctx context.Context, in *BroadcastRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取所有的ssrc
	GetAllSSRC(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SSRCListResponse, error)
	// 国标通道编辑时的数据回显
	GetRawChannel(ctx context.Context, in *GetRawChannelRequest, opts ...grpc.CallOption) (*Channel, error)
	// 添加平台信息
	AddPlatform(ctx context.Context, in *Platform, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取平台信息
	GetPlatform(ctx context.Context, in *GetPlatformRequest, opts ...grpc.CallOption) (*PlatformResponse, error)
	// 更新平台信息
	UpdatePlatform(ctx context.Context, in *Platform, opts ...grpc.CallOption) (*BaseResponse, error)
	// 删除平台信息
	DeletePlatform(ctx context.Context, in *DeletePlatformRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取平台列表
	ListPlatforms(ctx context.Context, in *ListPlatformsRequest, opts ...grpc.CallOption) (*PlatformsPageInfo, error)
	// 查询录像记录
	QueryRecord(ctx context.Context, in *QueryRecordRequest, opts ...grpc.CallOption) (*QueryRecordResponse, error)
	// PTZ 云台控制
	PtzControl(ctx context.Context, in *PtzControlRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 光圈控制
	IrisControl(ctx context.Context, in *IrisControlRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 聚焦控制
	FocusControl(ctx context.Context, in *FocusControlRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 查询预置位
	QueryPreset(ctx context.Context, in *PresetRequest, opts ...grpc.CallOption) (*PresetResponse, error)
	// 设置预置位
	AddPreset(ctx context.Context, in *PresetRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 调用预置位
	CallPreset(ctx context.Context, in *PresetRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 删除预置位
	DeletePreset(ctx context.Context, in *PresetRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 巡航点控制 - 添加巡航点
	AddCruisePoint(ctx context.Context, in *CruisePointRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 巡航点控制 - 删除巡航点
	DeleteCruisePoint(ctx context.Context, in *CruisePointRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 设置巡航速度
	SetCruiseSpeed(ctx context.Context, in *CruiseSpeedRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 设置巡航停留时间
	SetCruiseTime(ctx context.Context, in *CruiseTimeRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 开始巡航
	StartCruise(ctx context.Context, in *CruiseRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 停止巡航
	StopCruise(ctx context.Context, in *CruiseRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 开始自动扫描
	StartScan(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 停止自动扫描
	StopScan(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 设置自动扫描左边界
	SetScanLeft(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 设置自动扫描右边界
	SetScanRight(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 设置自动扫描速度
	SetScanSpeed(ctx context.Context, in *ScanSpeedRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 雨刷控制
	WiperControl(ctx context.Context, in *WiperControlRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 辅助开关控制
	AuxiliaryControl(ctx context.Context, in *AuxiliaryControlRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 测试SIP连接
	TestSip(ctx context.Context, in *TestSipRequest, opts ...grpc.CallOption) (*TestSipResponse, error)
	// 分页查询报警记录
	SearchAlarms(ctx context.Context, in *SearchAlarmsRequest, opts ...grpc.CallOption) (*SearchAlarmsResponse, error)
	// 添加平台通道
	AddPlatformChannel(ctx context.Context, in *AddPlatformChannelRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 录制控制
	Recording(ctx context.Context, in *RecordingRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 接收JPEG文件
	UploadJpeg(ctx context.Context, in *UploadJpegRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 更新通道信息
	UpdateChannel(ctx context.Context, in *UpdateChannelRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 回放暂停
	PlaybackPause(ctx context.Context, in *PlaybackPauseRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 回放恢复
	PlaybackResume(ctx context.Context, in *PlaybackResumeRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 回放拖动播放
	PlaybackSeek(ctx context.Context, in *PlaybackSeekRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 回放倍速播放
	PlaybackSpeed(ctx context.Context, in *PlaybackSpeedRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取单个分组
	GetGroups(ctx context.Context, in *GetGroupsRequest, opts ...grpc.CallOption) (*GroupsListResponse, error)
	// 添加分组
	AddGroup(ctx context.Context, in *Group, opts ...grpc.CallOption) (*BaseResponse, error)
	// 更新分组
	UpdateGroup(ctx context.Context, in *Group, opts ...grpc.CallOption) (*BaseResponse, error)
	// 删除分组
	DeleteGroup(ctx context.Context, in *DeleteGroupRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 添加分组与通道关联
	AddGroupChannel(ctx context.Context, in *AddGroupChannelRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 删除分组与通道关联
	DeleteGroupChannel(ctx context.Context, in *DeleteGroupChannelRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取分组下的通道列表
	GetGroupChannels(ctx context.Context, in *GetGroupChannelsRequest, opts ...grpc.CallOption) (*GroupChannelsResponse, error)
	// 删除设备
	RemoveDevice(ctx context.Context, in *RemoveDeviceRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 接收报警信息
	ReceiveAlarm(ctx context.Context, in *AlarmInfoRequest, opts ...grpc.CallOption) (*BaseResponse, error)
}

type apiClient struct {
	cc grpc.ClientConnInterface
}

func NewApiClient(cc grpc.ClientConnInterface) ApiClient {
	return &apiClient{cc}
}

func (c *apiClient) List(ctx context.Context, in *GetDevicesRequest, opts ...grpc.CallOption) (*DevicesPageInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DevicesPageInfo)
	err := c.cc.Invoke(ctx, Api_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetDevice(ctx context.Context, in *GetDeviceRequest, opts ...grpc.CallOption) (*DeviceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceResponse)
	err := c.cc.Invoke(ctx, Api_GetDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetDevices(ctx context.Context, in *GetDevicesRequest, opts ...grpc.CallOption) (*DevicesPageInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DevicesPageInfo)
	err := c.cc.Invoke(ctx, Api_GetDevices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetChannels(ctx context.Context, in *GetChannelsRequest, opts ...grpc.CallOption) (*ChannelsPageInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChannelsPageInfo)
	err := c.cc.Invoke(ctx, Api_GetChannels_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SyncDevice(ctx context.Context, in *SyncDeviceRequest, opts ...grpc.CallOption) (*SyncStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncStatus)
	err := c.cc.Invoke(ctx, Api_SyncDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DeleteDevice(ctx context.Context, in *DeleteDeviceRequest, opts ...grpc.CallOption) (*DeleteDeviceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDeviceResponse)
	err := c.cc.Invoke(ctx, Api_DeleteDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetSubChannels(ctx context.Context, in *GetSubChannelsRequest, opts ...grpc.CallOption) (*ChannelsPageInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChannelsPageInfo)
	err := c.cc.Invoke(ctx, Api_GetSubChannels_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) ChangeAudio(ctx context.Context, in *ChangeAudioRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_ChangeAudio_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdateChannelStreamIdentification(ctx context.Context, in *Channel, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_UpdateChannelStreamIdentification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdateTransport(ctx context.Context, in *UpdateTransportRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_UpdateTransport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddDevice(ctx context.Context, in *Device, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_AddDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdateDevice(ctx context.Context, in *Device, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_UpdateDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetDeviceStatus(ctx context.Context, in *GetDeviceStatusRequest, opts ...grpc.CallOption) (*DeviceStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceStatusResponse)
	err := c.cc.Invoke(ctx, Api_GetDeviceStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetDeviceAlarm(ctx context.Context, in *GetDeviceAlarmRequest, opts ...grpc.CallOption) (*DeviceAlarmResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceAlarmResponse)
	err := c.cc.Invoke(ctx, Api_GetDeviceAlarm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetSyncStatus(ctx context.Context, in *GetSyncStatusRequest, opts ...grpc.CallOption) (*SyncStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncStatus)
	err := c.cc.Invoke(ctx, Api_GetSyncStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetSubscribeInfo(ctx context.Context, in *GetSubscribeInfoRequest, opts ...grpc.CallOption) (*SubscribeInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubscribeInfoResponse)
	err := c.cc.Invoke(ctx, Api_GetSubscribeInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetSnap(ctx context.Context, in *GetSnapRequest, opts ...grpc.CallOption) (*SnapResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SnapResponse)
	err := c.cc.Invoke(ctx, Api_GetSnap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StopConvert(ctx context.Context, in *ConvertStopRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_StopConvert_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StartBroadcast(ctx context.Context, in *BroadcastRequest, opts ...grpc.CallOption) (*BroadcastResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BroadcastResponse)
	err := c.cc.Invoke(ctx, Api_StartBroadcast_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StopBroadcast(ctx context.Context, in *BroadcastRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_StopBroadcast_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetAllSSRC(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SSRCListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SSRCListResponse)
	err := c.cc.Invoke(ctx, Api_GetAllSSRC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetRawChannel(ctx context.Context, in *GetRawChannelRequest, opts ...grpc.CallOption) (*Channel, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Channel)
	err := c.cc.Invoke(ctx, Api_GetRawChannel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddPlatform(ctx context.Context, in *Platform, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_AddPlatform_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetPlatform(ctx context.Context, in *GetPlatformRequest, opts ...grpc.CallOption) (*PlatformResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlatformResponse)
	err := c.cc.Invoke(ctx, Api_GetPlatform_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdatePlatform(ctx context.Context, in *Platform, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_UpdatePlatform_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DeletePlatform(ctx context.Context, in *DeletePlatformRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_DeletePlatform_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) ListPlatforms(ctx context.Context, in *ListPlatformsRequest, opts ...grpc.CallOption) (*PlatformsPageInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlatformsPageInfo)
	err := c.cc.Invoke(ctx, Api_ListPlatforms_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) QueryRecord(ctx context.Context, in *QueryRecordRequest, opts ...grpc.CallOption) (*QueryRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRecordResponse)
	err := c.cc.Invoke(ctx, Api_QueryRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) PtzControl(ctx context.Context, in *PtzControlRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_PtzControl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) IrisControl(ctx context.Context, in *IrisControlRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_IrisControl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) FocusControl(ctx context.Context, in *FocusControlRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_FocusControl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) QueryPreset(ctx context.Context, in *PresetRequest, opts ...grpc.CallOption) (*PresetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PresetResponse)
	err := c.cc.Invoke(ctx, Api_QueryPreset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddPreset(ctx context.Context, in *PresetRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_AddPreset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) CallPreset(ctx context.Context, in *PresetRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_CallPreset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DeletePreset(ctx context.Context, in *PresetRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_DeletePreset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddCruisePoint(ctx context.Context, in *CruisePointRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_AddCruisePoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DeleteCruisePoint(ctx context.Context, in *CruisePointRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_DeleteCruisePoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SetCruiseSpeed(ctx context.Context, in *CruiseSpeedRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_SetCruiseSpeed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SetCruiseTime(ctx context.Context, in *CruiseTimeRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_SetCruiseTime_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StartCruise(ctx context.Context, in *CruiseRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_StartCruise_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StopCruise(ctx context.Context, in *CruiseRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_StopCruise_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StartScan(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_StartScan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StopScan(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_StopScan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SetScanLeft(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_SetScanLeft_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SetScanRight(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_SetScanRight_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SetScanSpeed(ctx context.Context, in *ScanSpeedRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_SetScanSpeed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) WiperControl(ctx context.Context, in *WiperControlRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_WiperControl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AuxiliaryControl(ctx context.Context, in *AuxiliaryControlRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_AuxiliaryControl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) TestSip(ctx context.Context, in *TestSipRequest, opts ...grpc.CallOption) (*TestSipResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TestSipResponse)
	err := c.cc.Invoke(ctx, Api_TestSip_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SearchAlarms(ctx context.Context, in *SearchAlarmsRequest, opts ...grpc.CallOption) (*SearchAlarmsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchAlarmsResponse)
	err := c.cc.Invoke(ctx, Api_SearchAlarms_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddPlatformChannel(ctx context.Context, in *AddPlatformChannelRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_AddPlatformChannel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) Recording(ctx context.Context, in *RecordingRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_Recording_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UploadJpeg(ctx context.Context, in *UploadJpegRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_UploadJpeg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdateChannel(ctx context.Context, in *UpdateChannelRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_UpdateChannel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) PlaybackPause(ctx context.Context, in *PlaybackPauseRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_PlaybackPause_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) PlaybackResume(ctx context.Context, in *PlaybackResumeRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_PlaybackResume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) PlaybackSeek(ctx context.Context, in *PlaybackSeekRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_PlaybackSeek_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) PlaybackSpeed(ctx context.Context, in *PlaybackSpeedRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_PlaybackSpeed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetGroups(ctx context.Context, in *GetGroupsRequest, opts ...grpc.CallOption) (*GroupsListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GroupsListResponse)
	err := c.cc.Invoke(ctx, Api_GetGroups_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddGroup(ctx context.Context, in *Group, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_AddGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdateGroup(ctx context.Context, in *Group, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_UpdateGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DeleteGroup(ctx context.Context, in *DeleteGroupRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_DeleteGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddGroupChannel(ctx context.Context, in *AddGroupChannelRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_AddGroupChannel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DeleteGroupChannel(ctx context.Context, in *DeleteGroupChannelRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_DeleteGroupChannel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetGroupChannels(ctx context.Context, in *GetGroupChannelsRequest, opts ...grpc.CallOption) (*GroupChannelsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GroupChannelsResponse)
	err := c.cc.Invoke(ctx, Api_GetGroupChannels_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) RemoveDevice(ctx context.Context, in *RemoveDeviceRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_RemoveDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) ReceiveAlarm(ctx context.Context, in *AlarmInfoRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, Api_ReceiveAlarm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiServer is the server API for Api service.
// All implementations must embed UnimplementedApiServer
// for forward compatibility.
type ApiServer interface {
	// 获取设备列表
	List(context.Context, *GetDevicesRequest) (*DevicesPageInfo, error)
	// 使用ID查询国标设备
	GetDevice(context.Context, *GetDeviceRequest) (*DeviceResponse, error)
	// 分页查询国标设备
	GetDevices(context.Context, *GetDevicesRequest) (*DevicesPageInfo, error)
	// 分页查询通道
	GetChannels(context.Context, *GetChannelsRequest) (*ChannelsPageInfo, error)
	// 同步设备通道
	SyncDevice(context.Context, *SyncDeviceRequest) (*SyncStatus, error)
	// 移除设备
	DeleteDevice(context.Context, *DeleteDeviceRequest) (*DeleteDeviceResponse, error)
	// 分页查询子目录通道
	GetSubChannels(context.Context, *GetSubChannelsRequest) (*ChannelsPageInfo, error)
	// 开启/关闭通道的音频
	ChangeAudio(context.Context, *ChangeAudioRequest) (*BaseResponse, error)
	// 修改通道的码流类型
	UpdateChannelStreamIdentification(context.Context, *Channel) (*BaseResponse, error)
	// 修改数据流传输模式
	UpdateTransport(context.Context, *UpdateTransportRequest) (*BaseResponse, error)
	// 添加设备信息
	AddDevice(context.Context, *Device) (*BaseResponse, error)
	// 更新设备信息
	UpdateDevice(context.Context, *Device) (*BaseResponse, error)
	// 设备状态查询
	GetDeviceStatus(context.Context, *GetDeviceStatusRequest) (*DeviceStatusResponse, error)
	// 设备报警查询
	GetDeviceAlarm(context.Context, *GetDeviceAlarmRequest) (*DeviceAlarmResponse, error)
	// 获取通道同步进度
	GetSyncStatus(context.Context, *GetSyncStatusRequest) (*SyncStatus, error)
	// 获取设备的订阅状态
	GetSubscribeInfo(context.Context, *GetSubscribeInfoRequest) (*SubscribeInfoResponse, error)
	// 请求截图
	GetSnap(context.Context, *GetSnapRequest) (*SnapResponse, error)
	// 结束转码
	StopConvert(context.Context, *ConvertStopRequest) (*BaseResponse, error)
	// 语音广播命令
	StartBroadcast(context.Context, *BroadcastRequest) (*BroadcastResponse, error)
	// 停止语音广播
	StopBroadcast(context.Context, *BroadcastRequest) (*BaseResponse, error)
	// 获取所有的ssrc
	GetAllSSRC(context.Context, *emptypb.Empty) (*SSRCListResponse, error)
	// 国标通道编辑时的数据回显
	GetRawChannel(context.Context, *GetRawChannelRequest) (*Channel, error)
	// 添加平台信息
	AddPlatform(context.Context, *Platform) (*BaseResponse, error)
	// 获取平台信息
	GetPlatform(context.Context, *GetPlatformRequest) (*PlatformResponse, error)
	// 更新平台信息
	UpdatePlatform(context.Context, *Platform) (*BaseResponse, error)
	// 删除平台信息
	DeletePlatform(context.Context, *DeletePlatformRequest) (*BaseResponse, error)
	// 获取平台列表
	ListPlatforms(context.Context, *ListPlatformsRequest) (*PlatformsPageInfo, error)
	// 查询录像记录
	QueryRecord(context.Context, *QueryRecordRequest) (*QueryRecordResponse, error)
	// PTZ 云台控制
	PtzControl(context.Context, *PtzControlRequest) (*BaseResponse, error)
	// 光圈控制
	IrisControl(context.Context, *IrisControlRequest) (*BaseResponse, error)
	// 聚焦控制
	FocusControl(context.Context, *FocusControlRequest) (*BaseResponse, error)
	// 查询预置位
	QueryPreset(context.Context, *PresetRequest) (*PresetResponse, error)
	// 设置预置位
	AddPreset(context.Context, *PresetRequest) (*BaseResponse, error)
	// 调用预置位
	CallPreset(context.Context, *PresetRequest) (*BaseResponse, error)
	// 删除预置位
	DeletePreset(context.Context, *PresetRequest) (*BaseResponse, error)
	// 巡航点控制 - 添加巡航点
	AddCruisePoint(context.Context, *CruisePointRequest) (*BaseResponse, error)
	// 巡航点控制 - 删除巡航点
	DeleteCruisePoint(context.Context, *CruisePointRequest) (*BaseResponse, error)
	// 设置巡航速度
	SetCruiseSpeed(context.Context, *CruiseSpeedRequest) (*BaseResponse, error)
	// 设置巡航停留时间
	SetCruiseTime(context.Context, *CruiseTimeRequest) (*BaseResponse, error)
	// 开始巡航
	StartCruise(context.Context, *CruiseRequest) (*BaseResponse, error)
	// 停止巡航
	StopCruise(context.Context, *CruiseRequest) (*BaseResponse, error)
	// 开始自动扫描
	StartScan(context.Context, *ScanRequest) (*BaseResponse, error)
	// 停止自动扫描
	StopScan(context.Context, *ScanRequest) (*BaseResponse, error)
	// 设置自动扫描左边界
	SetScanLeft(context.Context, *ScanRequest) (*BaseResponse, error)
	// 设置自动扫描右边界
	SetScanRight(context.Context, *ScanRequest) (*BaseResponse, error)
	// 设置自动扫描速度
	SetScanSpeed(context.Context, *ScanSpeedRequest) (*BaseResponse, error)
	// 雨刷控制
	WiperControl(context.Context, *WiperControlRequest) (*BaseResponse, error)
	// 辅助开关控制
	AuxiliaryControl(context.Context, *AuxiliaryControlRequest) (*BaseResponse, error)
	// 测试SIP连接
	TestSip(context.Context, *TestSipRequest) (*TestSipResponse, error)
	// 分页查询报警记录
	SearchAlarms(context.Context, *SearchAlarmsRequest) (*SearchAlarmsResponse, error)
	// 添加平台通道
	AddPlatformChannel(context.Context, *AddPlatformChannelRequest) (*BaseResponse, error)
	// 录制控制
	Recording(context.Context, *RecordingRequest) (*BaseResponse, error)
	// 接收JPEG文件
	UploadJpeg(context.Context, *UploadJpegRequest) (*BaseResponse, error)
	// 更新通道信息
	UpdateChannel(context.Context, *UpdateChannelRequest) (*BaseResponse, error)
	// 回放暂停
	PlaybackPause(context.Context, *PlaybackPauseRequest) (*BaseResponse, error)
	// 回放恢复
	PlaybackResume(context.Context, *PlaybackResumeRequest) (*BaseResponse, error)
	// 回放拖动播放
	PlaybackSeek(context.Context, *PlaybackSeekRequest) (*BaseResponse, error)
	// 回放倍速播放
	PlaybackSpeed(context.Context, *PlaybackSpeedRequest) (*BaseResponse, error)
	// 获取单个分组
	GetGroups(context.Context, *GetGroupsRequest) (*GroupsListResponse, error)
	// 添加分组
	AddGroup(context.Context, *Group) (*BaseResponse, error)
	// 更新分组
	UpdateGroup(context.Context, *Group) (*BaseResponse, error)
	// 删除分组
	DeleteGroup(context.Context, *DeleteGroupRequest) (*BaseResponse, error)
	// 添加分组与通道关联
	AddGroupChannel(context.Context, *AddGroupChannelRequest) (*BaseResponse, error)
	// 删除分组与通道关联
	DeleteGroupChannel(context.Context, *DeleteGroupChannelRequest) (*BaseResponse, error)
	// 获取分组下的通道列表
	GetGroupChannels(context.Context, *GetGroupChannelsRequest) (*GroupChannelsResponse, error)
	// 删除设备
	RemoveDevice(context.Context, *RemoveDeviceRequest) (*BaseResponse, error)
	// 接收报警信息
	ReceiveAlarm(context.Context, *AlarmInfoRequest) (*BaseResponse, error)
	mustEmbedUnimplementedApiServer()
}

// UnimplementedApiServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedApiServer struct{}

func (UnimplementedApiServer) List(context.Context, *GetDevicesRequest) (*DevicesPageInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedApiServer) GetDevice(context.Context, *GetDeviceRequest) (*DeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDevice not implemented")
}
func (UnimplementedApiServer) GetDevices(context.Context, *GetDevicesRequest) (*DevicesPageInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDevices not implemented")
}
func (UnimplementedApiServer) GetChannels(context.Context, *GetChannelsRequest) (*ChannelsPageInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannels not implemented")
}
func (UnimplementedApiServer) SyncDevice(context.Context, *SyncDeviceRequest) (*SyncStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncDevice not implemented")
}
func (UnimplementedApiServer) DeleteDevice(context.Context, *DeleteDeviceRequest) (*DeleteDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDevice not implemented")
}
func (UnimplementedApiServer) GetSubChannels(context.Context, *GetSubChannelsRequest) (*ChannelsPageInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubChannels not implemented")
}
func (UnimplementedApiServer) ChangeAudio(context.Context, *ChangeAudioRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeAudio not implemented")
}
func (UnimplementedApiServer) UpdateChannelStreamIdentification(context.Context, *Channel) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateChannelStreamIdentification not implemented")
}
func (UnimplementedApiServer) UpdateTransport(context.Context, *UpdateTransportRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTransport not implemented")
}
func (UnimplementedApiServer) AddDevice(context.Context, *Device) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddDevice not implemented")
}
func (UnimplementedApiServer) UpdateDevice(context.Context, *Device) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDevice not implemented")
}
func (UnimplementedApiServer) GetDeviceStatus(context.Context, *GetDeviceStatusRequest) (*DeviceStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceStatus not implemented")
}
func (UnimplementedApiServer) GetDeviceAlarm(context.Context, *GetDeviceAlarmRequest) (*DeviceAlarmResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceAlarm not implemented")
}
func (UnimplementedApiServer) GetSyncStatus(context.Context, *GetSyncStatusRequest) (*SyncStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyncStatus not implemented")
}
func (UnimplementedApiServer) GetSubscribeInfo(context.Context, *GetSubscribeInfoRequest) (*SubscribeInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscribeInfo not implemented")
}
func (UnimplementedApiServer) GetSnap(context.Context, *GetSnapRequest) (*SnapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSnap not implemented")
}
func (UnimplementedApiServer) StopConvert(context.Context, *ConvertStopRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopConvert not implemented")
}
func (UnimplementedApiServer) StartBroadcast(context.Context, *BroadcastRequest) (*BroadcastResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartBroadcast not implemented")
}
func (UnimplementedApiServer) StopBroadcast(context.Context, *BroadcastRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopBroadcast not implemented")
}
func (UnimplementedApiServer) GetAllSSRC(context.Context, *emptypb.Empty) (*SSRCListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllSSRC not implemented")
}
func (UnimplementedApiServer) GetRawChannel(context.Context, *GetRawChannelRequest) (*Channel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRawChannel not implemented")
}
func (UnimplementedApiServer) AddPlatform(context.Context, *Platform) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPlatform not implemented")
}
func (UnimplementedApiServer) GetPlatform(context.Context, *GetPlatformRequest) (*PlatformResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlatform not implemented")
}
func (UnimplementedApiServer) UpdatePlatform(context.Context, *Platform) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlatform not implemented")
}
func (UnimplementedApiServer) DeletePlatform(context.Context, *DeletePlatformRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePlatform not implemented")
}
func (UnimplementedApiServer) ListPlatforms(context.Context, *ListPlatformsRequest) (*PlatformsPageInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlatforms not implemented")
}
func (UnimplementedApiServer) QueryRecord(context.Context, *QueryRecordRequest) (*QueryRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRecord not implemented")
}
func (UnimplementedApiServer) PtzControl(context.Context, *PtzControlRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PtzControl not implemented")
}
func (UnimplementedApiServer) IrisControl(context.Context, *IrisControlRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IrisControl not implemented")
}
func (UnimplementedApiServer) FocusControl(context.Context, *FocusControlRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FocusControl not implemented")
}
func (UnimplementedApiServer) QueryPreset(context.Context, *PresetRequest) (*PresetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPreset not implemented")
}
func (UnimplementedApiServer) AddPreset(context.Context, *PresetRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPreset not implemented")
}
func (UnimplementedApiServer) CallPreset(context.Context, *PresetRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallPreset not implemented")
}
func (UnimplementedApiServer) DeletePreset(context.Context, *PresetRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePreset not implemented")
}
func (UnimplementedApiServer) AddCruisePoint(context.Context, *CruisePointRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCruisePoint not implemented")
}
func (UnimplementedApiServer) DeleteCruisePoint(context.Context, *CruisePointRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCruisePoint not implemented")
}
func (UnimplementedApiServer) SetCruiseSpeed(context.Context, *CruiseSpeedRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCruiseSpeed not implemented")
}
func (UnimplementedApiServer) SetCruiseTime(context.Context, *CruiseTimeRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCruiseTime not implemented")
}
func (UnimplementedApiServer) StartCruise(context.Context, *CruiseRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCruise not implemented")
}
func (UnimplementedApiServer) StopCruise(context.Context, *CruiseRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopCruise not implemented")
}
func (UnimplementedApiServer) StartScan(context.Context, *ScanRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartScan not implemented")
}
func (UnimplementedApiServer) StopScan(context.Context, *ScanRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopScan not implemented")
}
func (UnimplementedApiServer) SetScanLeft(context.Context, *ScanRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetScanLeft not implemented")
}
func (UnimplementedApiServer) SetScanRight(context.Context, *ScanRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetScanRight not implemented")
}
func (UnimplementedApiServer) SetScanSpeed(context.Context, *ScanSpeedRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetScanSpeed not implemented")
}
func (UnimplementedApiServer) WiperControl(context.Context, *WiperControlRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WiperControl not implemented")
}
func (UnimplementedApiServer) AuxiliaryControl(context.Context, *AuxiliaryControlRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuxiliaryControl not implemented")
}
func (UnimplementedApiServer) TestSip(context.Context, *TestSipRequest) (*TestSipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TestSip not implemented")
}
func (UnimplementedApiServer) SearchAlarms(context.Context, *SearchAlarmsRequest) (*SearchAlarmsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAlarms not implemented")
}
func (UnimplementedApiServer) AddPlatformChannel(context.Context, *AddPlatformChannelRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPlatformChannel not implemented")
}
func (UnimplementedApiServer) Recording(context.Context, *RecordingRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Recording not implemented")
}
func (UnimplementedApiServer) UploadJpeg(context.Context, *UploadJpegRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadJpeg not implemented")
}
func (UnimplementedApiServer) UpdateChannel(context.Context, *UpdateChannelRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateChannel not implemented")
}
func (UnimplementedApiServer) PlaybackPause(context.Context, *PlaybackPauseRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaybackPause not implemented")
}
func (UnimplementedApiServer) PlaybackResume(context.Context, *PlaybackResumeRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaybackResume not implemented")
}
func (UnimplementedApiServer) PlaybackSeek(context.Context, *PlaybackSeekRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaybackSeek not implemented")
}
func (UnimplementedApiServer) PlaybackSpeed(context.Context, *PlaybackSpeedRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaybackSpeed not implemented")
}
func (UnimplementedApiServer) GetGroups(context.Context, *GetGroupsRequest) (*GroupsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroups not implemented")
}
func (UnimplementedApiServer) AddGroup(context.Context, *Group) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGroup not implemented")
}
func (UnimplementedApiServer) UpdateGroup(context.Context, *Group) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGroup not implemented")
}
func (UnimplementedApiServer) DeleteGroup(context.Context, *DeleteGroupRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroup not implemented")
}
func (UnimplementedApiServer) AddGroupChannel(context.Context, *AddGroupChannelRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGroupChannel not implemented")
}
func (UnimplementedApiServer) DeleteGroupChannel(context.Context, *DeleteGroupChannelRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroupChannel not implemented")
}
func (UnimplementedApiServer) GetGroupChannels(context.Context, *GetGroupChannelsRequest) (*GroupChannelsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupChannels not implemented")
}
func (UnimplementedApiServer) RemoveDevice(context.Context, *RemoveDeviceRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveDevice not implemented")
}
func (UnimplementedApiServer) ReceiveAlarm(context.Context, *AlarmInfoRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveAlarm not implemented")
}
func (UnimplementedApiServer) mustEmbedUnimplementedApiServer() {}
func (UnimplementedApiServer) testEmbeddedByValue()             {}

// UnsafeApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiServer will
// result in compilation errors.
type UnsafeApiServer interface {
	mustEmbedUnimplementedApiServer()
}

func RegisterApiServer(s grpc.ServiceRegistrar, srv ApiServer) {
	// If the following call pancis, it indicates UnimplementedApiServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Api_ServiceDesc, srv)
}

func _Api_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDevicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).List(ctx, req.(*GetDevicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetDevice(ctx, req.(*GetDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetDevices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDevicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetDevices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetDevices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetDevices(ctx, req.(*GetDevicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetChannels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetChannels(ctx, req.(*GetChannelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SyncDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SyncDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SyncDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SyncDevice(ctx, req.(*SyncDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DeleteDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DeleteDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_DeleteDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DeleteDevice(ctx, req.(*DeleteDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetSubChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubChannelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetSubChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetSubChannels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetSubChannels(ctx, req.(*GetSubChannelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_ChangeAudio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeAudioRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ChangeAudio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_ChangeAudio_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ChangeAudio(ctx, req.(*ChangeAudioRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdateChannelStreamIdentification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Channel)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdateChannelStreamIdentification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdateChannelStreamIdentification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdateChannelStreamIdentification(ctx, req.(*Channel))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdateTransport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTransportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdateTransport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdateTransport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdateTransport(ctx, req.(*UpdateTransportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Device)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddDevice(ctx, req.(*Device))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdateDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Device)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdateDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdateDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdateDevice(ctx, req.(*Device))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetDeviceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetDeviceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetDeviceStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetDeviceStatus(ctx, req.(*GetDeviceStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetDeviceAlarm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceAlarmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetDeviceAlarm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetDeviceAlarm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetDeviceAlarm(ctx, req.(*GetDeviceAlarmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetSyncStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetSyncStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetSyncStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetSyncStatus(ctx, req.(*GetSyncStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetSubscribeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubscribeInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetSubscribeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetSubscribeInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetSubscribeInfo(ctx, req.(*GetSubscribeInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetSnap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSnapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetSnap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetSnap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetSnap(ctx, req.(*GetSnapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StopConvert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertStopRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StopConvert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StopConvert_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StopConvert(ctx, req.(*ConvertStopRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StartBroadcast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BroadcastRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StartBroadcast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StartBroadcast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StartBroadcast(ctx, req.(*BroadcastRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StopBroadcast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BroadcastRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StopBroadcast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StopBroadcast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StopBroadcast(ctx, req.(*BroadcastRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetAllSSRC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetAllSSRC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetAllSSRC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetAllSSRC(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetRawChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRawChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetRawChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetRawChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetRawChannel(ctx, req.(*GetRawChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddPlatform_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Platform)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddPlatform(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddPlatform_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddPlatform(ctx, req.(*Platform))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetPlatform_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlatformRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetPlatform(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetPlatform_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetPlatform(ctx, req.(*GetPlatformRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdatePlatform_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Platform)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdatePlatform(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdatePlatform_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdatePlatform(ctx, req.(*Platform))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DeletePlatform_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePlatformRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DeletePlatform(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_DeletePlatform_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DeletePlatform(ctx, req.(*DeletePlatformRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_ListPlatforms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPlatformsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ListPlatforms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_ListPlatforms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ListPlatforms(ctx, req.(*ListPlatformsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_QueryRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).QueryRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_QueryRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).QueryRecord(ctx, req.(*QueryRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_PtzControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PtzControlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).PtzControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_PtzControl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).PtzControl(ctx, req.(*PtzControlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_IrisControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IrisControlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).IrisControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_IrisControl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).IrisControl(ctx, req.(*IrisControlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_FocusControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FocusControlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).FocusControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_FocusControl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).FocusControl(ctx, req.(*FocusControlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_QueryPreset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).QueryPreset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_QueryPreset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).QueryPreset(ctx, req.(*PresetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddPreset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddPreset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddPreset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddPreset(ctx, req.(*PresetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_CallPreset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).CallPreset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_CallPreset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).CallPreset(ctx, req.(*PresetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DeletePreset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DeletePreset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_DeletePreset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DeletePreset(ctx, req.(*PresetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddCruisePoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CruisePointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddCruisePoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddCruisePoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddCruisePoint(ctx, req.(*CruisePointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DeleteCruisePoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CruisePointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DeleteCruisePoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_DeleteCruisePoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DeleteCruisePoint(ctx, req.(*CruisePointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SetCruiseSpeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CruiseSpeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SetCruiseSpeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SetCruiseSpeed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SetCruiseSpeed(ctx, req.(*CruiseSpeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SetCruiseTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CruiseTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SetCruiseTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SetCruiseTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SetCruiseTime(ctx, req.(*CruiseTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StartCruise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CruiseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StartCruise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StartCruise_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StartCruise(ctx, req.(*CruiseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StopCruise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CruiseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StopCruise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StopCruise_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StopCruise(ctx, req.(*CruiseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StartScan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StartScan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StartScan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StartScan(ctx, req.(*ScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StopScan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StopScan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StopScan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StopScan(ctx, req.(*ScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SetScanLeft_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SetScanLeft(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SetScanLeft_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SetScanLeft(ctx, req.(*ScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SetScanRight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SetScanRight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SetScanRight_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SetScanRight(ctx, req.(*ScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SetScanSpeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanSpeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SetScanSpeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SetScanSpeed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SetScanSpeed(ctx, req.(*ScanSpeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_WiperControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WiperControlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).WiperControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_WiperControl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).WiperControl(ctx, req.(*WiperControlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AuxiliaryControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuxiliaryControlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AuxiliaryControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AuxiliaryControl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AuxiliaryControl(ctx, req.(*AuxiliaryControlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_TestSip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestSipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).TestSip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_TestSip_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).TestSip(ctx, req.(*TestSipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SearchAlarms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAlarmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SearchAlarms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SearchAlarms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SearchAlarms(ctx, req.(*SearchAlarmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddPlatformChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPlatformChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddPlatformChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddPlatformChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddPlatformChannel(ctx, req.(*AddPlatformChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_Recording_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).Recording(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_Recording_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).Recording(ctx, req.(*RecordingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UploadJpeg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadJpegRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UploadJpeg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UploadJpeg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UploadJpeg(ctx, req.(*UploadJpegRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdateChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdateChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdateChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdateChannel(ctx, req.(*UpdateChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_PlaybackPause_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaybackPauseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).PlaybackPause(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_PlaybackPause_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).PlaybackPause(ctx, req.(*PlaybackPauseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_PlaybackResume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaybackResumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).PlaybackResume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_PlaybackResume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).PlaybackResume(ctx, req.(*PlaybackResumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_PlaybackSeek_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaybackSeekRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).PlaybackSeek(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_PlaybackSeek_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).PlaybackSeek(ctx, req.(*PlaybackSeekRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_PlaybackSpeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaybackSpeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).PlaybackSpeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_PlaybackSpeed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).PlaybackSpeed(ctx, req.(*PlaybackSpeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetGroups(ctx, req.(*GetGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Group)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddGroup(ctx, req.(*Group))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdateGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Group)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdateGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdateGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdateGroup(ctx, req.(*Group))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DeleteGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DeleteGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_DeleteGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DeleteGroup(ctx, req.(*DeleteGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddGroupChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGroupChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddGroupChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddGroupChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddGroupChannel(ctx, req.(*AddGroupChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DeleteGroupChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DeleteGroupChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_DeleteGroupChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DeleteGroupChannel(ctx, req.(*DeleteGroupChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetGroupChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupChannelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetGroupChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetGroupChannels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetGroupChannels(ctx, req.(*GetGroupChannelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_RemoveDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).RemoveDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_RemoveDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).RemoveDevice(ctx, req.(*RemoveDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_ReceiveAlarm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AlarmInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ReceiveAlarm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_ReceiveAlarm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ReceiveAlarm(ctx, req.(*AlarmInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Api_ServiceDesc is the grpc.ServiceDesc for Api service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Api_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "gb28181pro.api",
	HandlerType: (*ApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _Api_List_Handler,
		},
		{
			MethodName: "GetDevice",
			Handler:    _Api_GetDevice_Handler,
		},
		{
			MethodName: "GetDevices",
			Handler:    _Api_GetDevices_Handler,
		},
		{
			MethodName: "GetChannels",
			Handler:    _Api_GetChannels_Handler,
		},
		{
			MethodName: "SyncDevice",
			Handler:    _Api_SyncDevice_Handler,
		},
		{
			MethodName: "DeleteDevice",
			Handler:    _Api_DeleteDevice_Handler,
		},
		{
			MethodName: "GetSubChannels",
			Handler:    _Api_GetSubChannels_Handler,
		},
		{
			MethodName: "ChangeAudio",
			Handler:    _Api_ChangeAudio_Handler,
		},
		{
			MethodName: "UpdateChannelStreamIdentification",
			Handler:    _Api_UpdateChannelStreamIdentification_Handler,
		},
		{
			MethodName: "UpdateTransport",
			Handler:    _Api_UpdateTransport_Handler,
		},
		{
			MethodName: "AddDevice",
			Handler:    _Api_AddDevice_Handler,
		},
		{
			MethodName: "UpdateDevice",
			Handler:    _Api_UpdateDevice_Handler,
		},
		{
			MethodName: "GetDeviceStatus",
			Handler:    _Api_GetDeviceStatus_Handler,
		},
		{
			MethodName: "GetDeviceAlarm",
			Handler:    _Api_GetDeviceAlarm_Handler,
		},
		{
			MethodName: "GetSyncStatus",
			Handler:    _Api_GetSyncStatus_Handler,
		},
		{
			MethodName: "GetSubscribeInfo",
			Handler:    _Api_GetSubscribeInfo_Handler,
		},
		{
			MethodName: "GetSnap",
			Handler:    _Api_GetSnap_Handler,
		},
		{
			MethodName: "StopConvert",
			Handler:    _Api_StopConvert_Handler,
		},
		{
			MethodName: "StartBroadcast",
			Handler:    _Api_StartBroadcast_Handler,
		},
		{
			MethodName: "StopBroadcast",
			Handler:    _Api_StopBroadcast_Handler,
		},
		{
			MethodName: "GetAllSSRC",
			Handler:    _Api_GetAllSSRC_Handler,
		},
		{
			MethodName: "GetRawChannel",
			Handler:    _Api_GetRawChannel_Handler,
		},
		{
			MethodName: "AddPlatform",
			Handler:    _Api_AddPlatform_Handler,
		},
		{
			MethodName: "GetPlatform",
			Handler:    _Api_GetPlatform_Handler,
		},
		{
			MethodName: "UpdatePlatform",
			Handler:    _Api_UpdatePlatform_Handler,
		},
		{
			MethodName: "DeletePlatform",
			Handler:    _Api_DeletePlatform_Handler,
		},
		{
			MethodName: "ListPlatforms",
			Handler:    _Api_ListPlatforms_Handler,
		},
		{
			MethodName: "QueryRecord",
			Handler:    _Api_QueryRecord_Handler,
		},
		{
			MethodName: "PtzControl",
			Handler:    _Api_PtzControl_Handler,
		},
		{
			MethodName: "IrisControl",
			Handler:    _Api_IrisControl_Handler,
		},
		{
			MethodName: "FocusControl",
			Handler:    _Api_FocusControl_Handler,
		},
		{
			MethodName: "QueryPreset",
			Handler:    _Api_QueryPreset_Handler,
		},
		{
			MethodName: "AddPreset",
			Handler:    _Api_AddPreset_Handler,
		},
		{
			MethodName: "CallPreset",
			Handler:    _Api_CallPreset_Handler,
		},
		{
			MethodName: "DeletePreset",
			Handler:    _Api_DeletePreset_Handler,
		},
		{
			MethodName: "AddCruisePoint",
			Handler:    _Api_AddCruisePoint_Handler,
		},
		{
			MethodName: "DeleteCruisePoint",
			Handler:    _Api_DeleteCruisePoint_Handler,
		},
		{
			MethodName: "SetCruiseSpeed",
			Handler:    _Api_SetCruiseSpeed_Handler,
		},
		{
			MethodName: "SetCruiseTime",
			Handler:    _Api_SetCruiseTime_Handler,
		},
		{
			MethodName: "StartCruise",
			Handler:    _Api_StartCruise_Handler,
		},
		{
			MethodName: "StopCruise",
			Handler:    _Api_StopCruise_Handler,
		},
		{
			MethodName: "StartScan",
			Handler:    _Api_StartScan_Handler,
		},
		{
			MethodName: "StopScan",
			Handler:    _Api_StopScan_Handler,
		},
		{
			MethodName: "SetScanLeft",
			Handler:    _Api_SetScanLeft_Handler,
		},
		{
			MethodName: "SetScanRight",
			Handler:    _Api_SetScanRight_Handler,
		},
		{
			MethodName: "SetScanSpeed",
			Handler:    _Api_SetScanSpeed_Handler,
		},
		{
			MethodName: "WiperControl",
			Handler:    _Api_WiperControl_Handler,
		},
		{
			MethodName: "AuxiliaryControl",
			Handler:    _Api_AuxiliaryControl_Handler,
		},
		{
			MethodName: "TestSip",
			Handler:    _Api_TestSip_Handler,
		},
		{
			MethodName: "SearchAlarms",
			Handler:    _Api_SearchAlarms_Handler,
		},
		{
			MethodName: "AddPlatformChannel",
			Handler:    _Api_AddPlatformChannel_Handler,
		},
		{
			MethodName: "Recording",
			Handler:    _Api_Recording_Handler,
		},
		{
			MethodName: "UploadJpeg",
			Handler:    _Api_UploadJpeg_Handler,
		},
		{
			MethodName: "UpdateChannel",
			Handler:    _Api_UpdateChannel_Handler,
		},
		{
			MethodName: "PlaybackPause",
			Handler:    _Api_PlaybackPause_Handler,
		},
		{
			MethodName: "PlaybackResume",
			Handler:    _Api_PlaybackResume_Handler,
		},
		{
			MethodName: "PlaybackSeek",
			Handler:    _Api_PlaybackSeek_Handler,
		},
		{
			MethodName: "PlaybackSpeed",
			Handler:    _Api_PlaybackSpeed_Handler,
		},
		{
			MethodName: "GetGroups",
			Handler:    _Api_GetGroups_Handler,
		},
		{
			MethodName: "AddGroup",
			Handler:    _Api_AddGroup_Handler,
		},
		{
			MethodName: "UpdateGroup",
			Handler:    _Api_UpdateGroup_Handler,
		},
		{
			MethodName: "DeleteGroup",
			Handler:    _Api_DeleteGroup_Handler,
		},
		{
			MethodName: "AddGroupChannel",
			Handler:    _Api_AddGroupChannel_Handler,
		},
		{
			MethodName: "DeleteGroupChannel",
			Handler:    _Api_DeleteGroupChannel_Handler,
		},
		{
			MethodName: "GetGroupChannels",
			Handler:    _Api_GetGroupChannels_Handler,
		},
		{
			MethodName: "RemoveDevice",
			Handler:    _Api_RemoveDevice_Handler,
		},
		{
			MethodName: "ReceiveAlarm",
			Handler:    _Api_ReceiveAlarm_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gb28181.proto",
}
