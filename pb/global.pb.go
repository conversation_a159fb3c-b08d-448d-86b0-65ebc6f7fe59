// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: global.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DisabledPluginsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*PluginInfo          `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisabledPluginsResponse) Reset() {
	*x = DisabledPluginsResponse{}
	mi := &file_global_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisabledPluginsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisabledPluginsResponse) ProtoMessage() {}

func (x *DisabledPluginsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisabledPluginsResponse.ProtoReflect.Descriptor instead.
func (*DisabledPluginsResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{0}
}

func (x *DisabledPluginsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DisabledPluginsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DisabledPluginsResponse) GetData() []*PluginInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConfigRequest) Reset() {
	*x = GetConfigRequest{}
	mi := &file_global_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigRequest) ProtoMessage() {}

func (x *GetConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigRequest.ProtoReflect.Descriptor instead.
func (*GetConfigRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{1}
}

func (x *GetConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Formily struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Type           string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Properties     map[string]*Formily    `protobuf:"bytes,2,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Component      string                 `protobuf:"bytes,3,opt,name=component,proto3" json:"component,omitempty"`
	ComponentProps map[string]*anypb.Any  `protobuf:"bytes,4,rep,name=componentProps,proto3" json:"componentProps,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Formily) Reset() {
	*x = Formily{}
	mi := &file_global_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Formily) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Formily) ProtoMessage() {}

func (x *Formily) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Formily.ProtoReflect.Descriptor instead.
func (*Formily) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{2}
}

func (x *Formily) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Formily) GetProperties() map[string]*Formily {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *Formily) GetComponent() string {
	if x != nil {
		return x.Component
	}
	return ""
}

func (x *Formily) GetComponentProps() map[string]*anypb.Any {
	if x != nil {
		return x.ComponentProps
	}
	return nil
}

type FormilyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Properties    map[string]*Formily    `protobuf:"bytes,2,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FormilyResponse) Reset() {
	*x = FormilyResponse{}
	mi := &file_global_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FormilyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormilyResponse) ProtoMessage() {}

func (x *FormilyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormilyResponse.ProtoReflect.Descriptor instead.
func (*FormilyResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{3}
}

func (x *FormilyResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *FormilyResponse) GetProperties() map[string]*Formily {
	if x != nil {
		return x.Properties
	}
	return nil
}

type ConfigData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	File          string                 `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
	Modified      string                 `protobuf:"bytes,2,opt,name=modified,proto3" json:"modified,omitempty"`
	Merged        string                 `protobuf:"bytes,3,opt,name=merged,proto3" json:"merged,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConfigData) Reset() {
	*x = ConfigData{}
	mi := &file_global_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfigData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigData) ProtoMessage() {}

func (x *ConfigData) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigData.ProtoReflect.Descriptor instead.
func (*ConfigData) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{4}
}

func (x *ConfigData) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *ConfigData) GetModified() string {
	if x != nil {
		return x.Modified
	}
	return ""
}

func (x *ConfigData) GetMerged() string {
	if x != nil {
		return x.Merged
	}
	return ""
}

type GetConfigFileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConfigFileResponse) Reset() {
	*x = GetConfigFileResponse{}
	mi := &file_global_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigFileResponse) ProtoMessage() {}

func (x *GetConfigFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigFileResponse.ProtoReflect.Descriptor instead.
func (*GetConfigFileResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{5}
}

func (x *GetConfigFileResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetConfigFileResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetConfigFileResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type GetConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *ConfigData            `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConfigResponse) Reset() {
	*x = GetConfigResponse{}
	mi := &file_global_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigResponse) ProtoMessage() {}

func (x *GetConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigResponse.ProtoReflect.Descriptor instead.
func (*GetConfigResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{6}
}

func (x *GetConfigResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetConfigResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetConfigResponse) GetData() *ConfigData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateConfigFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateConfigFileRequest) Reset() {
	*x = UpdateConfigFileRequest{}
	mi := &file_global_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateConfigFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConfigFileRequest) ProtoMessage() {}

func (x *UpdateConfigFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConfigFileRequest.ProtoReflect.Descriptor instead.
func (*UpdateConfigFileRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateConfigFileRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ModifyConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Yaml          string                 `protobuf:"bytes,2,opt,name=yaml,proto3" json:"yaml,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModifyConfigRequest) Reset() {
	*x = ModifyConfigRequest{}
	mi := &file_global_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyConfigRequest) ProtoMessage() {}

func (x *ModifyConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyConfigRequest.ProtoReflect.Descriptor instead.
func (*ModifyConfigRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{8}
}

func (x *ModifyConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModifyConfigRequest) GetYaml() string {
	if x != nil {
		return x.Yaml
	}
	return ""
}

type NetWorkInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Receive       uint64                 `protobuf:"varint,2,opt,name=receive,proto3" json:"receive,omitempty"`
	Sent          uint64                 `protobuf:"varint,3,opt,name=sent,proto3" json:"sent,omitempty"`
	ReceiveSpeed  uint64                 `protobuf:"varint,4,opt,name=receiveSpeed,proto3" json:"receiveSpeed,omitempty"`
	SentSpeed     uint64                 `protobuf:"varint,5,opt,name=sentSpeed,proto3" json:"sentSpeed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetWorkInfo) Reset() {
	*x = NetWorkInfo{}
	mi := &file_global_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetWorkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetWorkInfo) ProtoMessage() {}

func (x *NetWorkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetWorkInfo.ProtoReflect.Descriptor instead.
func (*NetWorkInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{9}
}

func (x *NetWorkInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NetWorkInfo) GetReceive() uint64 {
	if x != nil {
		return x.Receive
	}
	return 0
}

func (x *NetWorkInfo) GetSent() uint64 {
	if x != nil {
		return x.Sent
	}
	return 0
}

func (x *NetWorkInfo) GetReceiveSpeed() uint64 {
	if x != nil {
		return x.ReceiveSpeed
	}
	return 0
}

func (x *NetWorkInfo) GetSentSpeed() uint64 {
	if x != nil {
		return x.SentSpeed
	}
	return 0
}

type Usage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Free          uint64                 `protobuf:"varint,2,opt,name=free,proto3" json:"free,omitempty"`
	Used          uint64                 `protobuf:"varint,3,opt,name=used,proto3" json:"used,omitempty"`
	Usage         float32                `protobuf:"fixed32,4,opt,name=usage,proto3" json:"usage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Usage) Reset() {
	*x = Usage{}
	mi := &file_global_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Usage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Usage) ProtoMessage() {}

func (x *Usage) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Usage.ProtoReflect.Descriptor instead.
func (*Usage) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{10}
}

func (x *Usage) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Usage) GetFree() uint64 {
	if x != nil {
		return x.Free
	}
	return 0
}

func (x *Usage) GetUsed() uint64 {
	if x != nil {
		return x.Used
	}
	return 0
}

func (x *Usage) GetUsage() float32 {
	if x != nil {
		return x.Usage
	}
	return 0
}

type SummaryResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Address        string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Memory         *Usage                 `protobuf:"bytes,2,opt,name=memory,proto3" json:"memory,omitempty"`
	CpuUsage       float32                `protobuf:"fixed32,3,opt,name=cpuUsage,proto3" json:"cpuUsage,omitempty"`
	HardDisk       *Usage                 `protobuf:"bytes,4,opt,name=hardDisk,proto3" json:"hardDisk,omitempty"`
	NetWork        []*NetWorkInfo         `protobuf:"bytes,5,rep,name=netWork,proto3" json:"netWork,omitempty"`
	StreamCount    int32                  `protobuf:"varint,6,opt,name=streamCount,proto3" json:"streamCount,omitempty"`
	SubscribeCount int32                  `protobuf:"varint,7,opt,name=subscribeCount,proto3" json:"subscribeCount,omitempty"`
	PullCount      int32                  `protobuf:"varint,8,opt,name=pullCount,proto3" json:"pullCount,omitempty"`
	PushCount      int32                  `protobuf:"varint,9,opt,name=pushCount,proto3" json:"pushCount,omitempty"`
	RecordCount    int32                  `protobuf:"varint,10,opt,name=recordCount,proto3" json:"recordCount,omitempty"`
	TransformCount int32                  `protobuf:"varint,11,opt,name=transformCount,proto3" json:"transformCount,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SummaryResponse) Reset() {
	*x = SummaryResponse{}
	mi := &file_global_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SummaryResponse) ProtoMessage() {}

func (x *SummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SummaryResponse.ProtoReflect.Descriptor instead.
func (*SummaryResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{11}
}

func (x *SummaryResponse) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SummaryResponse) GetMemory() *Usage {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *SummaryResponse) GetCpuUsage() float32 {
	if x != nil {
		return x.CpuUsage
	}
	return 0
}

func (x *SummaryResponse) GetHardDisk() *Usage {
	if x != nil {
		return x.HardDisk
	}
	return nil
}

func (x *SummaryResponse) GetNetWork() []*NetWorkInfo {
	if x != nil {
		return x.NetWork
	}
	return nil
}

func (x *SummaryResponse) GetStreamCount() int32 {
	if x != nil {
		return x.StreamCount
	}
	return 0
}

func (x *SummaryResponse) GetSubscribeCount() int32 {
	if x != nil {
		return x.SubscribeCount
	}
	return 0
}

func (x *SummaryResponse) GetPullCount() int32 {
	if x != nil {
		return x.PullCount
	}
	return 0
}

func (x *SummaryResponse) GetPushCount() int32 {
	if x != nil {
		return x.PushCount
	}
	return 0
}

func (x *SummaryResponse) GetRecordCount() int32 {
	if x != nil {
		return x.RecordCount
	}
	return 0
}

func (x *SummaryResponse) GetTransformCount() int32 {
	if x != nil {
		return x.TransformCount
	}
	return 0
}

type PluginInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PushAddr      []string               `protobuf:"bytes,2,rep,name=pushAddr,proto3" json:"pushAddr,omitempty"`
	PlayAddr      []string               `protobuf:"bytes,3,rep,name=playAddr,proto3" json:"playAddr,omitempty"`
	Description   map[string]string      `protobuf:"bytes,4,rep,name=description,proto3" json:"description,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginInfo) Reset() {
	*x = PluginInfo{}
	mi := &file_global_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginInfo) ProtoMessage() {}

func (x *PluginInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginInfo.ProtoReflect.Descriptor instead.
func (*PluginInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{12}
}

func (x *PluginInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PluginInfo) GetPushAddr() []string {
	if x != nil {
		return x.PushAddr
	}
	return nil
}

func (x *PluginInfo) GetPlayAddr() []string {
	if x != nil {
		return x.PlayAddr
	}
	return nil
}

func (x *PluginInfo) GetDescription() map[string]string {
	if x != nil {
		return x.Description
	}
	return nil
}

type SysInfoData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	LocalIP       string                 `protobuf:"bytes,2,opt,name=localIP,proto3" json:"localIP,omitempty"`
	PublicIP      string                 `protobuf:"bytes,3,opt,name=publicIP,proto3" json:"publicIP,omitempty"`
	Version       string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	GoVersion     string                 `protobuf:"bytes,5,opt,name=goVersion,proto3" json:"goVersion,omitempty"`
	Os            string                 `protobuf:"bytes,6,opt,name=os,proto3" json:"os,omitempty"`
	Arch          string                 `protobuf:"bytes,7,opt,name=arch,proto3" json:"arch,omitempty"`
	Cpus          int32                  `protobuf:"varint,8,opt,name=cpus,proto3" json:"cpus,omitempty"`
	Plugins       []*PluginInfo          `protobuf:"bytes,9,rep,name=plugins,proto3" json:"plugins,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SysInfoData) Reset() {
	*x = SysInfoData{}
	mi := &file_global_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SysInfoData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SysInfoData) ProtoMessage() {}

func (x *SysInfoData) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SysInfoData.ProtoReflect.Descriptor instead.
func (*SysInfoData) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{13}
}

func (x *SysInfoData) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *SysInfoData) GetLocalIP() string {
	if x != nil {
		return x.LocalIP
	}
	return ""
}

func (x *SysInfoData) GetPublicIP() string {
	if x != nil {
		return x.PublicIP
	}
	return ""
}

func (x *SysInfoData) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *SysInfoData) GetGoVersion() string {
	if x != nil {
		return x.GoVersion
	}
	return ""
}

func (x *SysInfoData) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *SysInfoData) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *SysInfoData) GetCpus() int32 {
	if x != nil {
		return x.Cpus
	}
	return 0
}

func (x *SysInfoData) GetPlugins() []*PluginInfo {
	if x != nil {
		return x.Plugins
	}
	return nil
}

type SysInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *SysInfoData           `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SysInfoResponse) Reset() {
	*x = SysInfoResponse{}
	mi := &file_global_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SysInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SysInfoResponse) ProtoMessage() {}

func (x *SysInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SysInfoResponse.ProtoReflect.Descriptor instead.
func (*SysInfoResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{14}
}

func (x *SysInfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SysInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SysInfoResponse) GetData() *SysInfoData {
	if x != nil {
		return x.Data
	}
	return nil
}

type TaskTreeData struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type             uint32                 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Owner            string                 `protobuf:"bytes,3,opt,name=owner,proto3" json:"owner,omitempty"`
	StartTime        *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	Description      map[string]string      `protobuf:"bytes,5,rep,name=description,proto3" json:"description,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Children         []*TaskTreeData        `protobuf:"bytes,6,rep,name=children,proto3" json:"children,omitempty"`
	State            uint32                 `protobuf:"varint,7,opt,name=state,proto3" json:"state,omitempty"`
	Blocked          *TaskTreeData          `protobuf:"bytes,8,opt,name=blocked,proto3" json:"blocked,omitempty"`
	Pointer          uint64                 `protobuf:"varint,9,opt,name=pointer,proto3" json:"pointer,omitempty"`
	StartReason      string                 `protobuf:"bytes,10,opt,name=startReason,proto3" json:"startReason,omitempty"`
	EventLoopRunning bool                   `protobuf:"varint,11,opt,name=eventLoopRunning,proto3" json:"eventLoopRunning,omitempty"`
	Level            uint32                 `protobuf:"varint,12,opt,name=level,proto3" json:"level,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TaskTreeData) Reset() {
	*x = TaskTreeData{}
	mi := &file_global_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskTreeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskTreeData) ProtoMessage() {}

func (x *TaskTreeData) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskTreeData.ProtoReflect.Descriptor instead.
func (*TaskTreeData) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{15}
}

func (x *TaskTreeData) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskTreeData) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TaskTreeData) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *TaskTreeData) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *TaskTreeData) GetDescription() map[string]string {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *TaskTreeData) GetChildren() []*TaskTreeData {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *TaskTreeData) GetState() uint32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *TaskTreeData) GetBlocked() *TaskTreeData {
	if x != nil {
		return x.Blocked
	}
	return nil
}

func (x *TaskTreeData) GetPointer() uint64 {
	if x != nil {
		return x.Pointer
	}
	return 0
}

func (x *TaskTreeData) GetStartReason() string {
	if x != nil {
		return x.StartReason
	}
	return ""
}

func (x *TaskTreeData) GetEventLoopRunning() bool {
	if x != nil {
		return x.EventLoopRunning
	}
	return false
}

func (x *TaskTreeData) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type TaskTreeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *TaskTreeData          `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskTreeResponse) Reset() {
	*x = TaskTreeResponse{}
	mi := &file_global_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskTreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskTreeResponse) ProtoMessage() {}

func (x *TaskTreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskTreeResponse.ProtoReflect.Descriptor instead.
func (*TaskTreeResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{16}
}

func (x *TaskTreeResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TaskTreeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskTreeResponse) GetData() *TaskTreeData {
	if x != nil {
		return x.Data
	}
	return nil
}

type StreamListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageNum       int32                  `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamListRequest) Reset() {
	*x = StreamListRequest{}
	mi := &file_global_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamListRequest) ProtoMessage() {}

func (x *StreamListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamListRequest.ProtoReflect.Descriptor instead.
func (*StreamListRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{17}
}

func (x *StreamListRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *StreamListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type StreamListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	PageNum       int32                  `protobuf:"varint,4,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      int32                  `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Data          []*StreamInfo          `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamListResponse) Reset() {
	*x = StreamListResponse{}
	mi := &file_global_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamListResponse) ProtoMessage() {}

func (x *StreamListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamListResponse.ProtoReflect.Descriptor instead.
func (*StreamListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{18}
}

func (x *StreamListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *StreamListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamListResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *StreamListResponse) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *StreamListResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *StreamListResponse) GetData() []*StreamInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type StreamWaitListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          map[string]int32       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamWaitListResponse) Reset() {
	*x = StreamWaitListResponse{}
	mi := &file_global_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamWaitListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamWaitListResponse) ProtoMessage() {}

func (x *StreamWaitListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamWaitListResponse.ProtoReflect.Descriptor instead.
func (*StreamWaitListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{19}
}

func (x *StreamWaitListResponse) GetList() map[string]int32 {
	if x != nil {
		return x.List
	}
	return nil
}

type StreamSnapRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamSnapRequest) Reset() {
	*x = StreamSnapRequest{}
	mi := &file_global_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamSnapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamSnapRequest) ProtoMessage() {}

func (x *StreamSnapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamSnapRequest.ProtoReflect.Descriptor instead.
func (*StreamSnapRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{20}
}

func (x *StreamSnapRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

type StreamInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *StreamInfo            `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamInfoResponse) Reset() {
	*x = StreamInfoResponse{}
	mi := &file_global_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInfoResponse) ProtoMessage() {}

func (x *StreamInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInfoResponse.ProtoReflect.Descriptor instead.
func (*StreamInfoResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{21}
}

func (x *StreamInfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *StreamInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamInfoResponse) GetData() *StreamInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type StreamInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Path          string                 `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	State         int32                  `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"`
	Subscribers   int32                  `protobuf:"varint,3,opt,name=subscribers,proto3" json:"subscribers,omitempty"`
	AudioTrack    *AudioTrackInfo        `protobuf:"bytes,4,opt,name=audioTrack,proto3" json:"audioTrack,omitempty"`
	VideoTrack    *VideoTrackInfo        `protobuf:"bytes,5,opt,name=videoTrack,proto3" json:"videoTrack,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=startTime,proto3" json:"startTime,omitempty"`
	PluginName    string                 `protobuf:"bytes,7,opt,name=pluginName,proto3" json:"pluginName,omitempty"`
	Type          string                 `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`
	Meta          string                 `protobuf:"bytes,9,opt,name=meta,proto3" json:"meta,omitempty"`
	IsPaused      bool                   `protobuf:"varint,10,opt,name=isPaused,proto3" json:"isPaused,omitempty"`
	Gop           int32                  `protobuf:"varint,11,opt,name=gop,proto3" json:"gop,omitempty"`
	Speed         float32                `protobuf:"fixed32,12,opt,name=speed,proto3" json:"speed,omitempty"`
	BufferTime    *durationpb.Duration   `protobuf:"bytes,13,opt,name=bufferTime,proto3" json:"bufferTime,omitempty"`
	StopOnIdle    bool                   `protobuf:"varint,14,opt,name=stopOnIdle,proto3" json:"stopOnIdle,omitempty"`
	Recording     []*RecordingDetail     `protobuf:"bytes,15,rep,name=recording,proto3" json:"recording,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamInfo) Reset() {
	*x = StreamInfo{}
	mi := &file_global_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInfo) ProtoMessage() {}

func (x *StreamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInfo.ProtoReflect.Descriptor instead.
func (*StreamInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{22}
}

func (x *StreamInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *StreamInfo) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *StreamInfo) GetSubscribers() int32 {
	if x != nil {
		return x.Subscribers
	}
	return 0
}

func (x *StreamInfo) GetAudioTrack() *AudioTrackInfo {
	if x != nil {
		return x.AudioTrack
	}
	return nil
}

func (x *StreamInfo) GetVideoTrack() *VideoTrackInfo {
	if x != nil {
		return x.VideoTrack
	}
	return nil
}

func (x *StreamInfo) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *StreamInfo) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *StreamInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *StreamInfo) GetMeta() string {
	if x != nil {
		return x.Meta
	}
	return ""
}

func (x *StreamInfo) GetIsPaused() bool {
	if x != nil {
		return x.IsPaused
	}
	return false
}

func (x *StreamInfo) GetGop() int32 {
	if x != nil {
		return x.Gop
	}
	return 0
}

func (x *StreamInfo) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *StreamInfo) GetBufferTime() *durationpb.Duration {
	if x != nil {
		return x.BufferTime
	}
	return nil
}

func (x *StreamInfo) GetStopOnIdle() bool {
	if x != nil {
		return x.StopOnIdle
	}
	return false
}

func (x *StreamInfo) GetRecording() []*RecordingDetail {
	if x != nil {
		return x.Recording
	}
	return nil
}

type RecordingDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FilePath      string                 `protobuf:"bytes,1,opt,name=filePath,proto3" json:"filePath,omitempty"`
	Mode          string                 `protobuf:"bytes,2,opt,name=mode,proto3" json:"mode,omitempty"`
	Fragment      *durationpb.Duration   `protobuf:"bytes,3,opt,name=fragment,proto3" json:"fragment,omitempty"`
	Append        bool                   `protobuf:"varint,4,opt,name=append,proto3" json:"append,omitempty"`
	PluginName    string                 `protobuf:"bytes,5,opt,name=pluginName,proto3" json:"pluginName,omitempty"`
	Pointer       uint64                 `protobuf:"varint,6,opt,name=pointer,proto3" json:"pointer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecordingDetail) Reset() {
	*x = RecordingDetail{}
	mi := &file_global_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecordingDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordingDetail) ProtoMessage() {}

func (x *RecordingDetail) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordingDetail.ProtoReflect.Descriptor instead.
func (*RecordingDetail) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{23}
}

func (x *RecordingDetail) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *RecordingDetail) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *RecordingDetail) GetFragment() *durationpb.Duration {
	if x != nil {
		return x.Fragment
	}
	return nil
}

func (x *RecordingDetail) GetAppend() bool {
	if x != nil {
		return x.Append
	}
	return false
}

func (x *RecordingDetail) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *RecordingDetail) GetPointer() uint64 {
	if x != nil {
		return x.Pointer
	}
	return 0
}

type Wrap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timestamp     uint32                 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Size          uint32                 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Wrap) Reset() {
	*x = Wrap{}
	mi := &file_global_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Wrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Wrap) ProtoMessage() {}

func (x *Wrap) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Wrap.ProtoReflect.Descriptor instead.
func (*Wrap) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{24}
}

func (x *Wrap) GetTimestamp() uint32 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Wrap) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Wrap) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type TrackSnapShot struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sequence      uint32                 `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	Timestamp     uint32                 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	WriteTime     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=writeTime,proto3" json:"writeTime,omitempty"`
	KeyFrame      bool                   `protobuf:"varint,4,opt,name=keyFrame,proto3" json:"keyFrame,omitempty"`
	Wrap          []*Wrap                `protobuf:"bytes,5,rep,name=wrap,proto3" json:"wrap,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackSnapShot) Reset() {
	*x = TrackSnapShot{}
	mi := &file_global_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackSnapShot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackSnapShot) ProtoMessage() {}

func (x *TrackSnapShot) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackSnapShot.ProtoReflect.Descriptor instead.
func (*TrackSnapShot) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{25}
}

func (x *TrackSnapShot) GetSequence() uint32 {
	if x != nil {
		return x.Sequence
	}
	return 0
}

func (x *TrackSnapShot) GetTimestamp() uint32 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *TrackSnapShot) GetWriteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.WriteTime
	}
	return nil
}

func (x *TrackSnapShot) GetKeyFrame() bool {
	if x != nil {
		return x.KeyFrame
	}
	return false
}

func (x *TrackSnapShot) GetWrap() []*Wrap {
	if x != nil {
		return x.Wrap
	}
	return nil
}

type MemoryBlock struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	S             uint32                 `protobuf:"varint,1,opt,name=s,proto3" json:"s,omitempty"`
	E             uint32                 `protobuf:"varint,2,opt,name=e,proto3" json:"e,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemoryBlock) Reset() {
	*x = MemoryBlock{}
	mi := &file_global_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoryBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoryBlock) ProtoMessage() {}

func (x *MemoryBlock) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoryBlock.ProtoReflect.Descriptor instead.
func (*MemoryBlock) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{26}
}

func (x *MemoryBlock) GetS() uint32 {
	if x != nil {
		return x.S
	}
	return 0
}

func (x *MemoryBlock) GetE() uint32 {
	if x != nil {
		return x.E
	}
	return 0
}

type MemoryBlockGroup struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Size          uint32                 `protobuf:"varint,1,opt,name=size,proto3" json:"size,omitempty"`
	List          []*MemoryBlock         `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemoryBlockGroup) Reset() {
	*x = MemoryBlockGroup{}
	mi := &file_global_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoryBlockGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoryBlockGroup) ProtoMessage() {}

func (x *MemoryBlockGroup) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoryBlockGroup.ProtoReflect.Descriptor instead.
func (*MemoryBlockGroup) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{27}
}

func (x *MemoryBlockGroup) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *MemoryBlockGroup) GetList() []*MemoryBlock {
	if x != nil {
		return x.List
	}
	return nil
}

type AudioTrackInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Codec         string                 `protobuf:"bytes,1,opt,name=codec,proto3" json:"codec,omitempty"`
	Delta         string                 `protobuf:"bytes,2,opt,name=delta,proto3" json:"delta,omitempty"`
	Meta          string                 `protobuf:"bytes,3,opt,name=meta,proto3" json:"meta,omitempty"`
	Bps           uint32                 `protobuf:"varint,4,opt,name=bps,proto3" json:"bps,omitempty"`
	BpsOut        uint32                 `protobuf:"varint,5,opt,name=bps_out,json=bpsOut,proto3" json:"bps_out,omitempty"`
	Fps           uint32                 `protobuf:"varint,6,opt,name=fps,proto3" json:"fps,omitempty"`
	SampleRate    uint32                 `protobuf:"varint,7,opt,name=sampleRate,proto3" json:"sampleRate,omitempty"`
	Channels      uint32                 `protobuf:"varint,8,opt,name=channels,proto3" json:"channels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioTrackInfo) Reset() {
	*x = AudioTrackInfo{}
	mi := &file_global_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioTrackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioTrackInfo) ProtoMessage() {}

func (x *AudioTrackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioTrackInfo.ProtoReflect.Descriptor instead.
func (*AudioTrackInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{28}
}

func (x *AudioTrackInfo) GetCodec() string {
	if x != nil {
		return x.Codec
	}
	return ""
}

func (x *AudioTrackInfo) GetDelta() string {
	if x != nil {
		return x.Delta
	}
	return ""
}

func (x *AudioTrackInfo) GetMeta() string {
	if x != nil {
		return x.Meta
	}
	return ""
}

func (x *AudioTrackInfo) GetBps() uint32 {
	if x != nil {
		return x.Bps
	}
	return 0
}

func (x *AudioTrackInfo) GetBpsOut() uint32 {
	if x != nil {
		return x.BpsOut
	}
	return 0
}

func (x *AudioTrackInfo) GetFps() uint32 {
	if x != nil {
		return x.Fps
	}
	return 0
}

func (x *AudioTrackInfo) GetSampleRate() uint32 {
	if x != nil {
		return x.SampleRate
	}
	return 0
}

func (x *AudioTrackInfo) GetChannels() uint32 {
	if x != nil {
		return x.Channels
	}
	return 0
}

type TrackSnapShotData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ring          []*TrackSnapShot       `protobuf:"bytes,1,rep,name=ring,proto3" json:"ring,omitempty"`
	RingDataSize  uint32                 `protobuf:"varint,2,opt,name=ringDataSize,proto3" json:"ringDataSize,omitempty"`
	Reader        map[uint32]uint32      `protobuf:"bytes,3,rep,name=reader,proto3" json:"reader,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	Memory        []*MemoryBlockGroup    `protobuf:"bytes,4,rep,name=memory,proto3" json:"memory,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackSnapShotData) Reset() {
	*x = TrackSnapShotData{}
	mi := &file_global_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackSnapShotData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackSnapShotData) ProtoMessage() {}

func (x *TrackSnapShotData) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackSnapShotData.ProtoReflect.Descriptor instead.
func (*TrackSnapShotData) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{29}
}

func (x *TrackSnapShotData) GetRing() []*TrackSnapShot {
	if x != nil {
		return x.Ring
	}
	return nil
}

func (x *TrackSnapShotData) GetRingDataSize() uint32 {
	if x != nil {
		return x.RingDataSize
	}
	return 0
}

func (x *TrackSnapShotData) GetReader() map[uint32]uint32 {
	if x != nil {
		return x.Reader
	}
	return nil
}

func (x *TrackSnapShotData) GetMemory() []*MemoryBlockGroup {
	if x != nil {
		return x.Memory
	}
	return nil
}

type TrackSnapShotResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *TrackSnapShotData     `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackSnapShotResponse) Reset() {
	*x = TrackSnapShotResponse{}
	mi := &file_global_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackSnapShotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackSnapShotResponse) ProtoMessage() {}

func (x *TrackSnapShotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackSnapShotResponse.ProtoReflect.Descriptor instead.
func (*TrackSnapShotResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{30}
}

func (x *TrackSnapShotResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TrackSnapShotResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TrackSnapShotResponse) GetData() *TrackSnapShotData {
	if x != nil {
		return x.Data
	}
	return nil
}

type VideoTrackInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Codec         string                 `protobuf:"bytes,1,opt,name=codec,proto3" json:"codec,omitempty"`
	Delta         string                 `protobuf:"bytes,2,opt,name=delta,proto3" json:"delta,omitempty"`
	Meta          string                 `protobuf:"bytes,3,opt,name=meta,proto3" json:"meta,omitempty"`
	Bps           uint32                 `protobuf:"varint,4,opt,name=bps,proto3" json:"bps,omitempty"`
	BpsOut        uint32                 `protobuf:"varint,5,opt,name=bps_out,json=bpsOut,proto3" json:"bps_out,omitempty"`
	Fps           uint32                 `protobuf:"varint,6,opt,name=fps,proto3" json:"fps,omitempty"`
	Width         uint32                 `protobuf:"varint,7,opt,name=width,proto3" json:"width,omitempty"`
	Height        uint32                 `protobuf:"varint,8,opt,name=height,proto3" json:"height,omitempty"`
	Gop           uint32                 `protobuf:"varint,9,opt,name=gop,proto3" json:"gop,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoTrackInfo) Reset() {
	*x = VideoTrackInfo{}
	mi := &file_global_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoTrackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoTrackInfo) ProtoMessage() {}

func (x *VideoTrackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoTrackInfo.ProtoReflect.Descriptor instead.
func (*VideoTrackInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{31}
}

func (x *VideoTrackInfo) GetCodec() string {
	if x != nil {
		return x.Codec
	}
	return ""
}

func (x *VideoTrackInfo) GetDelta() string {
	if x != nil {
		return x.Delta
	}
	return ""
}

func (x *VideoTrackInfo) GetMeta() string {
	if x != nil {
		return x.Meta
	}
	return ""
}

func (x *VideoTrackInfo) GetBps() uint32 {
	if x != nil {
		return x.Bps
	}
	return 0
}

func (x *VideoTrackInfo) GetBpsOut() uint32 {
	if x != nil {
		return x.BpsOut
	}
	return 0
}

func (x *VideoTrackInfo) GetFps() uint32 {
	if x != nil {
		return x.Fps
	}
	return 0
}

func (x *VideoTrackInfo) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *VideoTrackInfo) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VideoTrackInfo) GetGop() uint32 {
	if x != nil {
		return x.Gop
	}
	return 0
}

type SuccessResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuccessResponse) Reset() {
	*x = SuccessResponse{}
	mi := &file_global_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuccessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuccessResponse) ProtoMessage() {}

func (x *SuccessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuccessResponse.ProtoReflect.Descriptor instead.
func (*SuccessResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{32}
}

func (x *SuccessResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SuccessResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RequestWithId struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StreamPath    string                 `protobuf:"bytes,2,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestWithId) Reset() {
	*x = RequestWithId{}
	mi := &file_global_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestWithId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestWithId) ProtoMessage() {}

func (x *RequestWithId) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestWithId.ProtoReflect.Descriptor instead.
func (*RequestWithId) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{33}
}

func (x *RequestWithId) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RequestWithId) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

type RequestWithId64 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestWithId64) Reset() {
	*x = RequestWithId64{}
	mi := &file_global_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestWithId64) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestWithId64) ProtoMessage() {}

func (x *RequestWithId64) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestWithId64.ProtoReflect.Descriptor instead.
func (*RequestWithId64) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{34}
}

func (x *RequestWithId64) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ChangeSubscribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StreamPath    string                 `protobuf:"bytes,2,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeSubscribeRequest) Reset() {
	*x = ChangeSubscribeRequest{}
	mi := &file_global_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeSubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeSubscribeRequest) ProtoMessage() {}

func (x *ChangeSubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeSubscribeRequest.ProtoReflect.Descriptor instead.
func (*ChangeSubscribeRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{35}
}

func (x *ChangeSubscribeRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeSubscribeRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

type SubscribersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	PageNum       int32                  `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribersRequest) Reset() {
	*x = SubscribersRequest{}
	mi := &file_global_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribersRequest) ProtoMessage() {}

func (x *SubscribersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribersRequest.ProtoReflect.Descriptor instead.
func (*SubscribersRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{36}
}

func (x *SubscribersRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *SubscribersRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *SubscribersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type RingReaderSnapShot struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sequence      uint32                 `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	Timestamp     uint32                 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Delay         uint32                 `protobuf:"varint,3,opt,name=delay,proto3" json:"delay,omitempty"`
	State         int32                  `protobuf:"varint,4,opt,name=state,proto3" json:"state,omitempty"`
	Bps           uint32                 `protobuf:"varint,5,opt,name=bps,proto3" json:"bps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RingReaderSnapShot) Reset() {
	*x = RingReaderSnapShot{}
	mi := &file_global_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RingReaderSnapShot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RingReaderSnapShot) ProtoMessage() {}

func (x *RingReaderSnapShot) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RingReaderSnapShot.ProtoReflect.Descriptor instead.
func (*RingReaderSnapShot) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{37}
}

func (x *RingReaderSnapShot) GetSequence() uint32 {
	if x != nil {
		return x.Sequence
	}
	return 0
}

func (x *RingReaderSnapShot) GetTimestamp() uint32 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RingReaderSnapShot) GetDelay() uint32 {
	if x != nil {
		return x.Delay
	}
	return 0
}

func (x *RingReaderSnapShot) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *RingReaderSnapShot) GetBps() uint32 {
	if x != nil {
		return x.Bps
	}
	return 0
}

type SubscriberSnapShot struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	AudioReader   *RingReaderSnapShot    `protobuf:"bytes,3,opt,name=audioReader,proto3" json:"audioReader,omitempty"`
	VideoReader   *RingReaderSnapShot    `protobuf:"bytes,4,opt,name=videoReader,proto3" json:"videoReader,omitempty"`
	Meta          string                 `protobuf:"bytes,5,opt,name=meta,proto3" json:"meta,omitempty"`
	BufferTime    *durationpb.Duration   `protobuf:"bytes,6,opt,name=bufferTime,proto3" json:"bufferTime,omitempty"`
	SubMode       int32                  `protobuf:"varint,7,opt,name=subMode,proto3" json:"subMode,omitempty"`
	SyncMode      int32                  `protobuf:"varint,8,opt,name=syncMode,proto3" json:"syncMode,omitempty"`
	PluginName    string                 `protobuf:"bytes,9,opt,name=pluginName,proto3" json:"pluginName,omitempty"`
	Type          string                 `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"`
	RemoteAddr    string                 `protobuf:"bytes,11,opt,name=remoteAddr,proto3" json:"remoteAddr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriberSnapShot) Reset() {
	*x = SubscriberSnapShot{}
	mi := &file_global_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriberSnapShot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriberSnapShot) ProtoMessage() {}

func (x *SubscriberSnapShot) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriberSnapShot.ProtoReflect.Descriptor instead.
func (*SubscriberSnapShot) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{38}
}

func (x *SubscriberSnapShot) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubscriberSnapShot) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *SubscriberSnapShot) GetAudioReader() *RingReaderSnapShot {
	if x != nil {
		return x.AudioReader
	}
	return nil
}

func (x *SubscriberSnapShot) GetVideoReader() *RingReaderSnapShot {
	if x != nil {
		return x.VideoReader
	}
	return nil
}

func (x *SubscriberSnapShot) GetMeta() string {
	if x != nil {
		return x.Meta
	}
	return ""
}

func (x *SubscriberSnapShot) GetBufferTime() *durationpb.Duration {
	if x != nil {
		return x.BufferTime
	}
	return nil
}

func (x *SubscriberSnapShot) GetSubMode() int32 {
	if x != nil {
		return x.SubMode
	}
	return 0
}

func (x *SubscriberSnapShot) GetSyncMode() int32 {
	if x != nil {
		return x.SyncMode
	}
	return 0
}

func (x *SubscriberSnapShot) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *SubscriberSnapShot) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SubscriberSnapShot) GetRemoteAddr() string {
	if x != nil {
		return x.RemoteAddr
	}
	return ""
}

type SubscribersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	PageNum       int32                  `protobuf:"varint,4,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      int32                  `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Data          []*SubscriberSnapShot  `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribersResponse) Reset() {
	*x = SubscribersResponse{}
	mi := &file_global_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribersResponse) ProtoMessage() {}

func (x *SubscribersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribersResponse.ProtoReflect.Descriptor instead.
func (*SubscribersResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{39}
}

func (x *SubscribersResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SubscribersResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SubscribersResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SubscribersResponse) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *SubscribersResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SubscribersResponse) GetData() []*SubscriberSnapShot {
	if x != nil {
		return x.Data
	}
	return nil
}

type PullProxyListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*PullProxyInfo       `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PullProxyListResponse) Reset() {
	*x = PullProxyListResponse{}
	mi := &file_global_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PullProxyListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullProxyListResponse) ProtoMessage() {}

func (x *PullProxyListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullProxyListResponse.ProtoReflect.Descriptor instead.
func (*PullProxyListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{40}
}

func (x *PullProxyListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PullProxyListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PullProxyListResponse) GetData() []*PullProxyInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type PullProxyInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ID             uint32                 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	CreateTime     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=updateTime,proto3" json:"updateTime,omitempty"`          // 更新时间
	ParentID       uint32                 `protobuf:"varint,4,opt,name=parentID,proto3" json:"parentID,omitempty"`             // 父设备ID
	Name           string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                      // 设备名称
	Type           string                 `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`                      // 设备类型
	Status         uint32                 `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`                 // 设备状态
	PullURL        string                 `protobuf:"bytes,8,opt,name=pullURL,proto3" json:"pullURL,omitempty"`                // 拉流地址
	PullOnStart    bool                   `protobuf:"varint,9,opt,name=pullOnStart,proto3" json:"pullOnStart,omitempty"`       // 启动时拉流
	StopOnIdle     bool                   `protobuf:"varint,10,opt,name=stopOnIdle,proto3" json:"stopOnIdle,omitempty"`        // 空闲时停止拉流
	Audio          bool                   `protobuf:"varint,11,opt,name=audio,proto3" json:"audio,omitempty"`                  // 是否拉取音频
	Description    string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`       // 设备描述
	RecordPath     string                 `protobuf:"bytes,13,opt,name=recordPath,proto3" json:"recordPath,omitempty"`         // 录制路径
	RecordFragment *durationpb.Duration   `protobuf:"bytes,14,opt,name=recordFragment,proto3" json:"recordFragment,omitempty"` // 录制片段长度
	Rtt            uint32                 `protobuf:"varint,15,opt,name=rtt,proto3" json:"rtt,omitempty"`                      // 平均RTT
	StreamPath     string                 `protobuf:"bytes,16,opt,name=streamPath,proto3" json:"streamPath,omitempty"`         // 流路径
	CheckInterval  *durationpb.Duration   `protobuf:"bytes,17,opt,name=checkInterval,proto3" json:"checkInterval,omitempty"`   // 检查间隔
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PullProxyInfo) Reset() {
	*x = PullProxyInfo{}
	mi := &file_global_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PullProxyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullProxyInfo) ProtoMessage() {}

func (x *PullProxyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullProxyInfo.ProtoReflect.Descriptor instead.
func (*PullProxyInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{41}
}

func (x *PullProxyInfo) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *PullProxyInfo) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *PullProxyInfo) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *PullProxyInfo) GetParentID() uint32 {
	if x != nil {
		return x.ParentID
	}
	return 0
}

func (x *PullProxyInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PullProxyInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PullProxyInfo) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PullProxyInfo) GetPullURL() string {
	if x != nil {
		return x.PullURL
	}
	return ""
}

func (x *PullProxyInfo) GetPullOnStart() bool {
	if x != nil {
		return x.PullOnStart
	}
	return false
}

func (x *PullProxyInfo) GetStopOnIdle() bool {
	if x != nil {
		return x.StopOnIdle
	}
	return false
}

func (x *PullProxyInfo) GetAudio() bool {
	if x != nil {
		return x.Audio
	}
	return false
}

func (x *PullProxyInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PullProxyInfo) GetRecordPath() string {
	if x != nil {
		return x.RecordPath
	}
	return ""
}

func (x *PullProxyInfo) GetRecordFragment() *durationpb.Duration {
	if x != nil {
		return x.RecordFragment
	}
	return nil
}

func (x *PullProxyInfo) GetRtt() uint32 {
	if x != nil {
		return x.Rtt
	}
	return 0
}

func (x *PullProxyInfo) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *PullProxyInfo) GetCheckInterval() *durationpb.Duration {
	if x != nil {
		return x.CheckInterval
	}
	return nil
}

type UpdatePullProxyRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ID             uint32                 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	ParentID       *uint32                `protobuf:"varint,2,opt,name=parentID,proto3,oneof" json:"parentID,omitempty"`             // 父设备ID
	Name           *string                `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`                      // 设备名称
	Type           *string                `protobuf:"bytes,4,opt,name=type,proto3,oneof" json:"type,omitempty"`                      // 设备类型
	Status         *uint32                `protobuf:"varint,5,opt,name=status,proto3,oneof" json:"status,omitempty"`                 // 设备状态
	PullURL        *string                `protobuf:"bytes,6,opt,name=pullURL,proto3,oneof" json:"pullURL,omitempty"`                // 拉流地址
	PullOnStart    *bool                  `protobuf:"varint,7,opt,name=pullOnStart,proto3,oneof" json:"pullOnStart,omitempty"`       // 启动时拉流
	StopOnIdle     *bool                  `protobuf:"varint,8,opt,name=stopOnIdle,proto3,oneof" json:"stopOnIdle,omitempty"`         // 空闲时停止拉流
	Audio          *bool                  `protobuf:"varint,9,opt,name=audio,proto3,oneof" json:"audio,omitempty"`                   // 是否拉取音频
	Description    *string                `protobuf:"bytes,10,opt,name=description,proto3,oneof" json:"description,omitempty"`       // 设备描述
	RecordPath     *string                `protobuf:"bytes,11,opt,name=recordPath,proto3,oneof" json:"recordPath,omitempty"`         // 录制路径
	RecordFragment *durationpb.Duration   `protobuf:"bytes,12,opt,name=recordFragment,proto3,oneof" json:"recordFragment,omitempty"` // 录制片段长度
	StreamPath     *string                `protobuf:"bytes,13,opt,name=streamPath,proto3,oneof" json:"streamPath,omitempty"`         // 流路径
	CheckInterval  *durationpb.Duration   `protobuf:"bytes,14,opt,name=checkInterval,proto3,oneof" json:"checkInterval,omitempty"`   // 检查间隔
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdatePullProxyRequest) Reset() {
	*x = UpdatePullProxyRequest{}
	mi := &file_global_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePullProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePullProxyRequest) ProtoMessage() {}

func (x *UpdatePullProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePullProxyRequest.ProtoReflect.Descriptor instead.
func (*UpdatePullProxyRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{42}
}

func (x *UpdatePullProxyRequest) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UpdatePullProxyRequest) GetParentID() uint32 {
	if x != nil && x.ParentID != nil {
		return *x.ParentID
	}
	return 0
}

func (x *UpdatePullProxyRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdatePullProxyRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *UpdatePullProxyRequest) GetStatus() uint32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *UpdatePullProxyRequest) GetPullURL() string {
	if x != nil && x.PullURL != nil {
		return *x.PullURL
	}
	return ""
}

func (x *UpdatePullProxyRequest) GetPullOnStart() bool {
	if x != nil && x.PullOnStart != nil {
		return *x.PullOnStart
	}
	return false
}

func (x *UpdatePullProxyRequest) GetStopOnIdle() bool {
	if x != nil && x.StopOnIdle != nil {
		return *x.StopOnIdle
	}
	return false
}

func (x *UpdatePullProxyRequest) GetAudio() bool {
	if x != nil && x.Audio != nil {
		return *x.Audio
	}
	return false
}

func (x *UpdatePullProxyRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdatePullProxyRequest) GetRecordPath() string {
	if x != nil && x.RecordPath != nil {
		return *x.RecordPath
	}
	return ""
}

func (x *UpdatePullProxyRequest) GetRecordFragment() *durationpb.Duration {
	if x != nil {
		return x.RecordFragment
	}
	return nil
}

func (x *UpdatePullProxyRequest) GetStreamPath() string {
	if x != nil && x.StreamPath != nil {
		return *x.StreamPath
	}
	return ""
}

func (x *UpdatePullProxyRequest) GetCheckInterval() *durationpb.Duration {
	if x != nil {
		return x.CheckInterval
	}
	return nil
}

type PushProxyInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            uint32                 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	ParentID      uint32                 `protobuf:"varint,4,opt,name=parentID,proto3" json:"parentID,omitempty"`       // 父设备ID
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                // 设备名称
	Type          string                 `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`                // 设备类型
	Status        uint32                 `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`           // 设备状态
	PushURL       string                 `protobuf:"bytes,8,opt,name=pushURL,proto3" json:"pushURL,omitempty"`          // 推流地址
	PushOnStart   bool                   `protobuf:"varint,9,opt,name=pushOnStart,proto3" json:"pushOnStart,omitempty"` // 启动时推流
	Audio         bool                   `protobuf:"varint,10,opt,name=audio,proto3" json:"audio,omitempty"`            // 是否推音频
	Description   string                 `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"` // 设备描述
	Rtt           uint32                 `protobuf:"varint,12,opt,name=rtt,proto3" json:"rtt,omitempty"`                // 平均RTT
	StreamPath    string                 `protobuf:"bytes,13,opt,name=streamPath,proto3" json:"streamPath,omitempty"`   // 流路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushProxyInfo) Reset() {
	*x = PushProxyInfo{}
	mi := &file_global_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushProxyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushProxyInfo) ProtoMessage() {}

func (x *PushProxyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushProxyInfo.ProtoReflect.Descriptor instead.
func (*PushProxyInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{43}
}

func (x *PushProxyInfo) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *PushProxyInfo) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *PushProxyInfo) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *PushProxyInfo) GetParentID() uint32 {
	if x != nil {
		return x.ParentID
	}
	return 0
}

func (x *PushProxyInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PushProxyInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PushProxyInfo) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PushProxyInfo) GetPushURL() string {
	if x != nil {
		return x.PushURL
	}
	return ""
}

func (x *PushProxyInfo) GetPushOnStart() bool {
	if x != nil {
		return x.PushOnStart
	}
	return false
}

func (x *PushProxyInfo) GetAudio() bool {
	if x != nil {
		return x.Audio
	}
	return false
}

func (x *PushProxyInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PushProxyInfo) GetRtt() uint32 {
	if x != nil {
		return x.Rtt
	}
	return 0
}

func (x *PushProxyInfo) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

type UpdatePushProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            uint32                 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	ParentID      *uint32                `protobuf:"varint,2,opt,name=parentID,proto3,oneof" json:"parentID,omitempty"`       // 父设备ID
	Name          *string                `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`                // 设备名称
	Type          *string                `protobuf:"bytes,4,opt,name=type,proto3,oneof" json:"type,omitempty"`                // 设备类型
	Status        *uint32                `protobuf:"varint,5,opt,name=status,proto3,oneof" json:"status,omitempty"`           // 设备状态
	PushURL       *string                `protobuf:"bytes,6,opt,name=pushURL,proto3,oneof" json:"pushURL,omitempty"`          // 推流地址
	PushOnStart   *bool                  `protobuf:"varint,7,opt,name=pushOnStart,proto3,oneof" json:"pushOnStart,omitempty"` // 启动时推流
	Audio         *bool                  `protobuf:"varint,8,opt,name=audio,proto3,oneof" json:"audio,omitempty"`             // 是否推音频
	Description   *string                `protobuf:"bytes,9,opt,name=description,proto3,oneof" json:"description,omitempty"`  // 设备描述
	Rtt           *uint32                `protobuf:"varint,10,opt,name=rtt,proto3,oneof" json:"rtt,omitempty"`                // 平均RTT
	StreamPath    *string                `protobuf:"bytes,11,opt,name=streamPath,proto3,oneof" json:"streamPath,omitempty"`   // 流路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePushProxyRequest) Reset() {
	*x = UpdatePushProxyRequest{}
	mi := &file_global_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePushProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePushProxyRequest) ProtoMessage() {}

func (x *UpdatePushProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePushProxyRequest.ProtoReflect.Descriptor instead.
func (*UpdatePushProxyRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{44}
}

func (x *UpdatePushProxyRequest) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UpdatePushProxyRequest) GetParentID() uint32 {
	if x != nil && x.ParentID != nil {
		return *x.ParentID
	}
	return 0
}

func (x *UpdatePushProxyRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdatePushProxyRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *UpdatePushProxyRequest) GetStatus() uint32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *UpdatePushProxyRequest) GetPushURL() string {
	if x != nil && x.PushURL != nil {
		return *x.PushURL
	}
	return ""
}

func (x *UpdatePushProxyRequest) GetPushOnStart() bool {
	if x != nil && x.PushOnStart != nil {
		return *x.PushOnStart
	}
	return false
}

func (x *UpdatePushProxyRequest) GetAudio() bool {
	if x != nil && x.Audio != nil {
		return *x.Audio
	}
	return false
}

func (x *UpdatePushProxyRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdatePushProxyRequest) GetRtt() uint32 {
	if x != nil && x.Rtt != nil {
		return *x.Rtt
	}
	return 0
}

func (x *UpdatePushProxyRequest) GetStreamPath() string {
	if x != nil && x.StreamPath != nil {
		return *x.StreamPath
	}
	return ""
}

type PushProxyListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*PushProxyInfo       `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushProxyListResponse) Reset() {
	*x = PushProxyListResponse{}
	mi := &file_global_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushProxyListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushProxyListResponse) ProtoMessage() {}

func (x *PushProxyListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushProxyListResponse.ProtoReflect.Descriptor instead.
func (*PushProxyListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{45}
}

func (x *PushProxyListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PushProxyListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PushProxyListResponse) GetData() []*PushProxyInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type SetStreamAliasRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Alias         string                 `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`
	AutoRemove    bool                   `protobuf:"varint,3,opt,name=autoRemove,proto3" json:"autoRemove,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetStreamAliasRequest) Reset() {
	*x = SetStreamAliasRequest{}
	mi := &file_global_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetStreamAliasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetStreamAliasRequest) ProtoMessage() {}

func (x *SetStreamAliasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetStreamAliasRequest.ProtoReflect.Descriptor instead.
func (*SetStreamAliasRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{46}
}

func (x *SetStreamAliasRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *SetStreamAliasRequest) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *SetStreamAliasRequest) GetAutoRemove() bool {
	if x != nil {
		return x.AutoRemove
	}
	return false
}

type StreamAlias struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Alias         string                 `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`
	AutoRemove    bool                   `protobuf:"varint,3,opt,name=autoRemove,proto3" json:"autoRemove,omitempty"`
	Status        uint32                 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamAlias) Reset() {
	*x = StreamAlias{}
	mi := &file_global_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamAlias) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamAlias) ProtoMessage() {}

func (x *StreamAlias) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamAlias.ProtoReflect.Descriptor instead.
func (*StreamAlias) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{47}
}

func (x *StreamAlias) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *StreamAlias) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *StreamAlias) GetAutoRemove() bool {
	if x != nil {
		return x.AutoRemove
	}
	return false
}

func (x *StreamAlias) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type StreamAliasListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*StreamAlias         `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamAliasListResponse) Reset() {
	*x = StreamAliasListResponse{}
	mi := &file_global_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamAliasListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamAliasListResponse) ProtoMessage() {}

func (x *StreamAliasListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamAliasListResponse.ProtoReflect.Descriptor instead.
func (*StreamAliasListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{48}
}

func (x *StreamAliasListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *StreamAliasListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamAliasListResponse) GetData() []*StreamAlias {
	if x != nil {
		return x.Data
	}
	return nil
}

type SetStreamSpeedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Speed         float32                `protobuf:"fixed32,2,opt,name=speed,proto3" json:"speed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetStreamSpeedRequest) Reset() {
	*x = SetStreamSpeedRequest{}
	mi := &file_global_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetStreamSpeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetStreamSpeedRequest) ProtoMessage() {}

func (x *SetStreamSpeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetStreamSpeedRequest.ProtoReflect.Descriptor instead.
func (*SetStreamSpeedRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{49}
}

func (x *SetStreamSpeedRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *SetStreamSpeedRequest) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type SeekStreamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	TimeStamp     uint32                 `protobuf:"varint,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SeekStreamRequest) Reset() {
	*x = SeekStreamRequest{}
	mi := &file_global_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SeekStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeekStreamRequest) ProtoMessage() {}

func (x *SeekStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeekStreamRequest.ProtoReflect.Descriptor instead.
func (*SeekStreamRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{50}
}

func (x *SeekStreamRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *SeekStreamRequest) GetTimeStamp() uint32 {
	if x != nil {
		return x.TimeStamp
	}
	return 0
}

type Recording struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Pointer       uint64                 `protobuf:"varint,4,opt,name=pointer,proto3" json:"pointer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Recording) Reset() {
	*x = Recording{}
	mi := &file_global_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Recording) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Recording) ProtoMessage() {}

func (x *Recording) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Recording.ProtoReflect.Descriptor instead.
func (*Recording) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{51}
}

func (x *Recording) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *Recording) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Recording) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Recording) GetPointer() uint64 {
	if x != nil {
		return x.Pointer
	}
	return 0
}

type RecordingListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*Recording           `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecordingListResponse) Reset() {
	*x = RecordingListResponse{}
	mi := &file_global_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecordingListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordingListResponse) ProtoMessage() {}

func (x *RecordingListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordingListResponse.ProtoReflect.Descriptor instead.
func (*RecordingListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{52}
}

func (x *RecordingListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RecordingListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RecordingListResponse) GetData() []*Recording {
	if x != nil {
		return x.Data
	}
	return nil
}

type PushInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	TargetURL     string                 `protobuf:"bytes,2,opt,name=targetURL,proto3" json:"targetURL,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushInfo) Reset() {
	*x = PushInfo{}
	mi := &file_global_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushInfo) ProtoMessage() {}

func (x *PushInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushInfo.ProtoReflect.Descriptor instead.
func (*PushInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{53}
}

func (x *PushInfo) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *PushInfo) GetTargetURL() string {
	if x != nil {
		return x.TargetURL
	}
	return ""
}

func (x *PushInfo) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *PushInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type PushListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*PushInfo            `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushListResponse) Reset() {
	*x = PushListResponse{}
	mi := &file_global_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushListResponse) ProtoMessage() {}

func (x *PushListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushListResponse.ProtoReflect.Descriptor instead.
func (*PushListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{54}
}

func (x *PushListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PushListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PushListResponse) GetData() []*PushInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type AddPushRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	TargetURL     string                 `protobuf:"bytes,2,opt,name=targetURL,proto3" json:"targetURL,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPushRequest) Reset() {
	*x = AddPushRequest{}
	mi := &file_global_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPushRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPushRequest) ProtoMessage() {}

func (x *AddPushRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPushRequest.ProtoReflect.Descriptor instead.
func (*AddPushRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{55}
}

func (x *AddPushRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *AddPushRequest) GetTargetURL() string {
	if x != nil {
		return x.TargetURL
	}
	return ""
}

type Transform struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Target        string                 `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	PluginName    string                 `protobuf:"bytes,3,opt,name=pluginName,proto3" json:"pluginName,omitempty"`
	Config        string                 `protobuf:"bytes,4,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Transform) Reset() {
	*x = Transform{}
	mi := &file_global_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transform) ProtoMessage() {}

func (x *Transform) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transform.ProtoReflect.Descriptor instead.
func (*Transform) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{56}
}

func (x *Transform) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *Transform) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *Transform) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *Transform) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

type TransformListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*Transform           `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransformListResponse) Reset() {
	*x = TransformListResponse{}
	mi := &file_global_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransformListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransformListResponse) ProtoMessage() {}

func (x *TransformListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransformListResponse.ProtoReflect.Descriptor instead.
func (*TransformListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{57}
}

func (x *TransformListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TransformListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TransformListResponse) GetData() []*Transform {
	if x != nil {
		return x.Data
	}
	return nil
}

type ReqRecordList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Range         string                 `protobuf:"bytes,2,opt,name=range,proto3" json:"range,omitempty"`
	Start         string                 `protobuf:"bytes,3,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,4,opt,name=end,proto3" json:"end,omitempty"`
	PageNum       uint32                 `protobuf:"varint,5,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	EventLevel    string                 `protobuf:"bytes,8,opt,name=eventLevel,proto3" json:"eventLevel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqRecordList) Reset() {
	*x = ReqRecordList{}
	mi := &file_global_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqRecordList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRecordList) ProtoMessage() {}

func (x *ReqRecordList) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRecordList.ProtoReflect.Descriptor instead.
func (*ReqRecordList) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{58}
}

func (x *ReqRecordList) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReqRecordList) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

func (x *ReqRecordList) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *ReqRecordList) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *ReqRecordList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReqRecordList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReqRecordList) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ReqRecordList) GetEventLevel() string {
	if x != nil {
		return x.EventLevel
	}
	return ""
}

type RecordFile struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FilePath      string                 `protobuf:"bytes,2,opt,name=filePath,proto3" json:"filePath,omitempty"`
	StreamPath    string                 `protobuf:"bytes,3,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecordFile) Reset() {
	*x = RecordFile{}
	mi := &file_global_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecordFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordFile) ProtoMessage() {}

func (x *RecordFile) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordFile.ProtoReflect.Descriptor instead.
func (*RecordFile) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{59}
}

func (x *RecordFile) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RecordFile) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *RecordFile) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *RecordFile) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *RecordFile) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type EventRecordFile struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FilePath      string                 `protobuf:"bytes,2,opt,name=filePath,proto3" json:"filePath,omitempty"`
	StreamPath    string                 `protobuf:"bytes,3,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
	EventId       string                 `protobuf:"bytes,6,opt,name=eventId,proto3" json:"eventId,omitempty"`
	EventLevel    string                 `protobuf:"bytes,7,opt,name=eventLevel,proto3" json:"eventLevel,omitempty"`
	EventName     string                 `protobuf:"bytes,8,opt,name=eventName,proto3" json:"eventName,omitempty"`
	EventDesc     string                 `protobuf:"bytes,9,opt,name=eventDesc,proto3" json:"eventDesc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventRecordFile) Reset() {
	*x = EventRecordFile{}
	mi := &file_global_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventRecordFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventRecordFile) ProtoMessage() {}

func (x *EventRecordFile) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventRecordFile.ProtoReflect.Descriptor instead.
func (*EventRecordFile) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{60}
}

func (x *EventRecordFile) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EventRecordFile) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *EventRecordFile) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *EventRecordFile) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *EventRecordFile) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *EventRecordFile) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *EventRecordFile) GetEventLevel() string {
	if x != nil {
		return x.EventLevel
	}
	return ""
}

func (x *EventRecordFile) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *EventRecordFile) GetEventDesc() string {
	if x != nil {
		return x.EventDesc
	}
	return ""
}

type RecordResponseList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         uint32                 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	PageNum       uint32                 `protobuf:"varint,4,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Data          []*RecordFile          `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecordResponseList) Reset() {
	*x = RecordResponseList{}
	mi := &file_global_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecordResponseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordResponseList) ProtoMessage() {}

func (x *RecordResponseList) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordResponseList.ProtoReflect.Descriptor instead.
func (*RecordResponseList) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{61}
}

func (x *RecordResponseList) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RecordResponseList) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RecordResponseList) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RecordResponseList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *RecordResponseList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *RecordResponseList) GetData() []*RecordFile {
	if x != nil {
		return x.Data
	}
	return nil
}

type EventRecordResponseList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         uint32                 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	PageNum       uint32                 `protobuf:"varint,4,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Data          []*EventRecordFile     `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventRecordResponseList) Reset() {
	*x = EventRecordResponseList{}
	mi := &file_global_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventRecordResponseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventRecordResponseList) ProtoMessage() {}

func (x *EventRecordResponseList) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventRecordResponseList.ProtoReflect.Descriptor instead.
func (*EventRecordResponseList) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{62}
}

func (x *EventRecordResponseList) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *EventRecordResponseList) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *EventRecordResponseList) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *EventRecordResponseList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *EventRecordResponseList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *EventRecordResponseList) GetData() []*EventRecordFile {
	if x != nil {
		return x.Data
	}
	return nil
}

type Catalog struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Count         uint32                 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Catalog) Reset() {
	*x = Catalog{}
	mi := &file_global_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Catalog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog) ProtoMessage() {}

func (x *Catalog) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog.ProtoReflect.Descriptor instead.
func (*Catalog) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{63}
}

func (x *Catalog) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *Catalog) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *Catalog) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Catalog) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type ResponseCatalog struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*Catalog             `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseCatalog) Reset() {
	*x = ResponseCatalog{}
	mi := &file_global_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseCatalog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseCatalog) ProtoMessage() {}

func (x *ResponseCatalog) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseCatalog.ProtoReflect.Descriptor instead.
func (*ResponseCatalog) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{64}
}

func (x *ResponseCatalog) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ResponseCatalog) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ResponseCatalog) GetData() []*Catalog {
	if x != nil {
		return x.Data
	}
	return nil
}

type ReqRecordDelete struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Ids           []uint32               `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	StartTime     string                 `protobuf:"bytes,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       string                 `protobuf:"bytes,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Range         string                 `protobuf:"bytes,5,opt,name=range,proto3" json:"range,omitempty"`
	Type          string                 `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqRecordDelete) Reset() {
	*x = ReqRecordDelete{}
	mi := &file_global_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqRecordDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRecordDelete) ProtoMessage() {}

func (x *ReqRecordDelete) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRecordDelete.ProtoReflect.Descriptor instead.
func (*ReqRecordDelete) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{65}
}

func (x *ReqRecordDelete) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReqRecordDelete) GetIds() []uint32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ReqRecordDelete) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ReqRecordDelete) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ReqRecordDelete) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

func (x *ReqRecordDelete) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type ResponseDelete struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*RecordFile          `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseDelete) Reset() {
	*x = ResponseDelete{}
	mi := &file_global_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseDelete) ProtoMessage() {}

func (x *ResponseDelete) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseDelete.ProtoReflect.Descriptor instead.
func (*ResponseDelete) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{66}
}

func (x *ResponseDelete) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ResponseDelete) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ResponseDelete) GetData() []*RecordFile {
	if x != nil {
		return x.Data
	}
	return nil
}

type ReqRecordCatalog struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqRecordCatalog) Reset() {
	*x = ReqRecordCatalog{}
	mi := &file_global_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqRecordCatalog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRecordCatalog) ProtoMessage() {}

func (x *ReqRecordCatalog) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRecordCatalog.ProtoReflect.Descriptor instead.
func (*ReqRecordCatalog) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{67}
}

func (x *ReqRecordCatalog) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type AlarmInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ServerInfo    string                 `protobuf:"bytes,2,opt,name=serverInfo,proto3" json:"serverInfo,omitempty"`
	StreamName    string                 `protobuf:"bytes,3,opt,name=streamName,proto3" json:"streamName,omitempty"`
	StreamPath    string                 `protobuf:"bytes,4,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	AlarmDesc     string                 `protobuf:"bytes,5,opt,name=alarmDesc,proto3" json:"alarmDesc,omitempty"`
	AlarmName     string                 `protobuf:"bytes,6,opt,name=alarmName,proto3" json:"alarmName,omitempty"`
	AlarmType     int32                  `protobuf:"varint,7,opt,name=alarmType,proto3" json:"alarmType,omitempty"`
	IsSent        bool                   `protobuf:"varint,8,opt,name=isSent,proto3" json:"isSent,omitempty"`
	FilePath      string                 `protobuf:"bytes,9,opt,name=filePath,proto3" json:"filePath,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlarmInfo) Reset() {
	*x = AlarmInfo{}
	mi := &file_global_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmInfo) ProtoMessage() {}

func (x *AlarmInfo) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmInfo.ProtoReflect.Descriptor instead.
func (*AlarmInfo) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{68}
}

func (x *AlarmInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AlarmInfo) GetServerInfo() string {
	if x != nil {
		return x.ServerInfo
	}
	return ""
}

func (x *AlarmInfo) GetStreamName() string {
	if x != nil {
		return x.StreamName
	}
	return ""
}

func (x *AlarmInfo) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *AlarmInfo) GetAlarmDesc() string {
	if x != nil {
		return x.AlarmDesc
	}
	return ""
}

func (x *AlarmInfo) GetAlarmName() string {
	if x != nil {
		return x.AlarmName
	}
	return ""
}

func (x *AlarmInfo) GetAlarmType() int32 {
	if x != nil {
		return x.AlarmType
	}
	return 0
}

func (x *AlarmInfo) GetIsSent() bool {
	if x != nil {
		return x.IsSent
	}
	return false
}

func (x *AlarmInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *AlarmInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AlarmInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AlarmListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageNum       int32                  `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Range         string                 `protobuf:"bytes,3,opt,name=range,proto3" json:"range,omitempty"`
	Start         string                 `protobuf:"bytes,4,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,5,opt,name=end,proto3" json:"end,omitempty"`
	AlarmType     int32                  `protobuf:"varint,6,opt,name=alarmType,proto3" json:"alarmType,omitempty"`
	StreamPath    string                 `protobuf:"bytes,7,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	StreamName    string                 `protobuf:"bytes,8,opt,name=streamName,proto3" json:"streamName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlarmListRequest) Reset() {
	*x = AlarmListRequest{}
	mi := &file_global_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmListRequest) ProtoMessage() {}

func (x *AlarmListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmListRequest.ProtoReflect.Descriptor instead.
func (*AlarmListRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{69}
}

func (x *AlarmListRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *AlarmListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AlarmListRequest) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

func (x *AlarmListRequest) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *AlarmListRequest) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *AlarmListRequest) GetAlarmType() int32 {
	if x != nil {
		return x.AlarmType
	}
	return 0
}

func (x *AlarmListRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *AlarmListRequest) GetStreamName() string {
	if x != nil {
		return x.StreamName
	}
	return ""
}

type AlarmListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	PageNum       int32                  `protobuf:"varint,4,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      int32                  `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Data          []*AlarmInfo           `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlarmListResponse) Reset() {
	*x = AlarmListResponse{}
	mi := &file_global_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmListResponse) ProtoMessage() {}

func (x *AlarmListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmListResponse.ProtoReflect.Descriptor instead.
func (*AlarmListResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{70}
}

func (x *AlarmListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AlarmListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AlarmListResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AlarmListResponse) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *AlarmListResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AlarmListResponse) GetData() []*AlarmInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type Step struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Error         string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=startedAt,proto3" json:"startedAt,omitempty"`
	CompletedAt   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=completedAt,proto3" json:"completedAt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Step) Reset() {
	*x = Step{}
	mi := &file_global_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Step) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Step) ProtoMessage() {}

func (x *Step) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Step.ProtoReflect.Descriptor instead.
func (*Step) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{71}
}

func (x *Step) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Step) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Step) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *Step) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *Step) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

type SubscriptionProgressData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Steps         []*Step                `protobuf:"bytes,1,rep,name=steps,proto3" json:"steps,omitempty"`
	CurrentStep   int32                  `protobuf:"varint,2,opt,name=currentStep,proto3" json:"currentStep,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriptionProgressData) Reset() {
	*x = SubscriptionProgressData{}
	mi := &file_global_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionProgressData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionProgressData) ProtoMessage() {}

func (x *SubscriptionProgressData) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionProgressData.ProtoReflect.Descriptor instead.
func (*SubscriptionProgressData) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{72}
}

func (x *SubscriptionProgressData) GetSteps() []*Step {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *SubscriptionProgressData) GetCurrentStep() int32 {
	if x != nil {
		return x.CurrentStep
	}
	return 0
}

type SubscriptionProgressResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *SubscriptionProgressData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriptionProgressResponse) Reset() {
	*x = SubscriptionProgressResponse{}
	mi := &file_global_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionProgressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionProgressResponse) ProtoMessage() {}

func (x *SubscriptionProgressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionProgressResponse.ProtoReflect.Descriptor instead.
func (*SubscriptionProgressResponse) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{73}
}

func (x *SubscriptionProgressResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SubscriptionProgressResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SubscriptionProgressResponse) GetData() *SubscriptionProgressData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GlobalPullRequest struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	RemoteURL  string                 `protobuf:"bytes,1,opt,name=remoteURL,proto3" json:"remoteURL,omitempty"`
	Protocol   string                 `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol,omitempty"`
	TestMode   int32                  `protobuf:"varint,3,opt,name=testMode,proto3" json:"testMode,omitempty"`    // 0: pull, 1: pull without publish
	StreamPath string                 `protobuf:"bytes,4,opt,name=streamPath,proto3" json:"streamPath,omitempty"` // 流路径
	// Publish configuration
	PubAudio          *bool                `protobuf:"varint,5,opt,name=pubAudio,proto3,oneof" json:"pubAudio,omitempty"`
	PubVideo          *bool                `protobuf:"varint,6,opt,name=pubVideo,proto3,oneof" json:"pubVideo,omitempty"`
	DelayCloseTimeout *durationpb.Duration `protobuf:"bytes,7,opt,name=delayCloseTimeout,proto3,oneof" json:"delayCloseTimeout,omitempty"` // 延迟自动关闭（无订阅时）
	Speed             *float64             `protobuf:"fixed64,8,opt,name=speed,proto3,oneof" json:"speed,omitempty"`                       // 发送速率
	MaxCount          *int32               `protobuf:"varint,9,opt,name=maxCount,proto3,oneof" json:"maxCount,omitempty"`                  // 最大发布者数量
	KickExist         *bool                `protobuf:"varint,10,opt,name=kickExist,proto3,oneof" json:"kickExist,omitempty"`               // 是否踢掉已经存在的发布者
	PublishTimeout    *durationpb.Duration `protobuf:"bytes,11,opt,name=publishTimeout,proto3,oneof" json:"publishTimeout,omitempty"`      // 发布无数据超时
	WaitCloseTimeout  *durationpb.Duration `protobuf:"bytes,12,opt,name=waitCloseTimeout,proto3,oneof" json:"waitCloseTimeout,omitempty"`  // 延迟自动关闭（等待重连）
	IdleTimeout       *durationpb.Duration `protobuf:"bytes,13,opt,name=idleTimeout,proto3,oneof" json:"idleTimeout,omitempty"`            // 空闲(无订阅)超时
	PauseTimeout      *durationpb.Duration `protobuf:"bytes,14,opt,name=pauseTimeout,proto3,oneof" json:"pauseTimeout,omitempty"`          // 暂停超时时间
	BufferTime        *durationpb.Duration `protobuf:"bytes,15,opt,name=bufferTime,proto3,oneof" json:"bufferTime,omitempty"`              // 缓冲时长，0代表取最近关键帧
	Scale             *float64             `protobuf:"fixed64,16,opt,name=scale,proto3,oneof" json:"scale,omitempty"`                      // 缩放倍数
	MaxFPS            *int32               `protobuf:"varint,17,opt,name=maxFPS,proto3,oneof" json:"maxFPS,omitempty"`                     // 最大FPS
	Key               *string              `protobuf:"bytes,18,opt,name=key,proto3,oneof" json:"key,omitempty"`                            // 发布鉴权key
	RelayMode         *string              `protobuf:"bytes,19,opt,name=relayMode,proto3,oneof" json:"relayMode,omitempty"`                // 转发模式
	PubType           *string              `protobuf:"bytes,20,opt,name=pubType,proto3,oneof" json:"pubType,omitempty"`                    // 发布类型
	Dump              *bool                `protobuf:"varint,21,opt,name=dump,proto3,oneof" json:"dump,omitempty"`                         // 是否dump
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GlobalPullRequest) Reset() {
	*x = GlobalPullRequest{}
	mi := &file_global_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GlobalPullRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalPullRequest) ProtoMessage() {}

func (x *GlobalPullRequest) ProtoReflect() protoreflect.Message {
	mi := &file_global_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalPullRequest.ProtoReflect.Descriptor instead.
func (*GlobalPullRequest) Descriptor() ([]byte, []int) {
	return file_global_proto_rawDescGZIP(), []int{74}
}

func (x *GlobalPullRequest) GetRemoteURL() string {
	if x != nil {
		return x.RemoteURL
	}
	return ""
}

func (x *GlobalPullRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *GlobalPullRequest) GetTestMode() int32 {
	if x != nil {
		return x.TestMode
	}
	return 0
}

func (x *GlobalPullRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *GlobalPullRequest) GetPubAudio() bool {
	if x != nil && x.PubAudio != nil {
		return *x.PubAudio
	}
	return false
}

func (x *GlobalPullRequest) GetPubVideo() bool {
	if x != nil && x.PubVideo != nil {
		return *x.PubVideo
	}
	return false
}

func (x *GlobalPullRequest) GetDelayCloseTimeout() *durationpb.Duration {
	if x != nil {
		return x.DelayCloseTimeout
	}
	return nil
}

func (x *GlobalPullRequest) GetSpeed() float64 {
	if x != nil && x.Speed != nil {
		return *x.Speed
	}
	return 0
}

func (x *GlobalPullRequest) GetMaxCount() int32 {
	if x != nil && x.MaxCount != nil {
		return *x.MaxCount
	}
	return 0
}

func (x *GlobalPullRequest) GetKickExist() bool {
	if x != nil && x.KickExist != nil {
		return *x.KickExist
	}
	return false
}

func (x *GlobalPullRequest) GetPublishTimeout() *durationpb.Duration {
	if x != nil {
		return x.PublishTimeout
	}
	return nil
}

func (x *GlobalPullRequest) GetWaitCloseTimeout() *durationpb.Duration {
	if x != nil {
		return x.WaitCloseTimeout
	}
	return nil
}

func (x *GlobalPullRequest) GetIdleTimeout() *durationpb.Duration {
	if x != nil {
		return x.IdleTimeout
	}
	return nil
}

func (x *GlobalPullRequest) GetPauseTimeout() *durationpb.Duration {
	if x != nil {
		return x.PauseTimeout
	}
	return nil
}

func (x *GlobalPullRequest) GetBufferTime() *durationpb.Duration {
	if x != nil {
		return x.BufferTime
	}
	return nil
}

func (x *GlobalPullRequest) GetScale() float64 {
	if x != nil && x.Scale != nil {
		return *x.Scale
	}
	return 0
}

func (x *GlobalPullRequest) GetMaxFPS() int32 {
	if x != nil && x.MaxFPS != nil {
		return *x.MaxFPS
	}
	return 0
}

func (x *GlobalPullRequest) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *GlobalPullRequest) GetRelayMode() string {
	if x != nil && x.RelayMode != nil {
		return *x.RelayMode
	}
	return ""
}

func (x *GlobalPullRequest) GetPubType() string {
	if x != nil && x.PubType != nil {
		return *x.PubType
	}
	return ""
}

func (x *GlobalPullRequest) GetDump() bool {
	if x != nil && x.Dump != nil {
		return *x.Dump
	}
	return false
}

var File_global_proto protoreflect.FileDescriptor

const file_global_proto_rawDesc = "" +
	"\n" +
	"\fglobal.proto\x12\x06global\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x19google/protobuf/any.proto\"o\n" +
	"\x17DisabledPluginsResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x04data\x18\x03 \x03(\v2\x12.global.PluginInfoR\x04data\"&\n" +
	"\x10GetConfigRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\xf2\x02\n" +
	"\aFormily\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12?\n" +
	"\n" +
	"properties\x18\x02 \x03(\v2\x1f.global.Formily.PropertiesEntryR\n" +
	"properties\x12\x1c\n" +
	"\tcomponent\x18\x03 \x01(\tR\tcomponent\x12K\n" +
	"\x0ecomponentProps\x18\x04 \x03(\v2#.global.Formily.ComponentPropsEntryR\x0ecomponentProps\x1aN\n" +
	"\x0fPropertiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12%\n" +
	"\x05value\x18\x02 \x01(\v2\x0f.global.FormilyR\x05value:\x028\x01\x1aW\n" +
	"\x13ComponentPropsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12*\n" +
	"\x05value\x18\x02 \x01(\v2\x14.google.protobuf.AnyR\x05value:\x028\x01\"\xbe\x01\n" +
	"\x0fFormilyResponse\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12G\n" +
	"\n" +
	"properties\x18\x02 \x03(\v2'.global.FormilyResponse.PropertiesEntryR\n" +
	"properties\x1aN\n" +
	"\x0fPropertiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12%\n" +
	"\x05value\x18\x02 \x01(\v2\x0f.global.FormilyR\x05value:\x028\x01\"T\n" +
	"\n" +
	"ConfigData\x12\x12\n" +
	"\x04file\x18\x01 \x01(\tR\x04file\x12\x1a\n" +
	"\bmodified\x18\x02 \x01(\tR\bmodified\x12\x16\n" +
	"\x06merged\x18\x03 \x01(\tR\x06merged\"Y\n" +
	"\x15GetConfigFileResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\"i\n" +
	"\x11GetConfigResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x04data\x18\x03 \x01(\v2\x12.global.ConfigDataR\x04data\"3\n" +
	"\x17UpdateConfigFileRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"=\n" +
	"\x13ModifyConfigRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04yaml\x18\x02 \x01(\tR\x04yaml\"\x91\x01\n" +
	"\vNetWorkInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\areceive\x18\x02 \x01(\x04R\areceive\x12\x12\n" +
	"\x04sent\x18\x03 \x01(\x04R\x04sent\x12\"\n" +
	"\freceiveSpeed\x18\x04 \x01(\x04R\freceiveSpeed\x12\x1c\n" +
	"\tsentSpeed\x18\x05 \x01(\x04R\tsentSpeed\"[\n" +
	"\x05Usage\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x04R\x05total\x12\x12\n" +
	"\x04free\x18\x02 \x01(\x04R\x04free\x12\x12\n" +
	"\x04used\x18\x03 \x01(\x04R\x04used\x12\x14\n" +
	"\x05usage\x18\x04 \x01(\x02R\x05usage\"\x98\x03\n" +
	"\x0fSummaryResponse\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x12%\n" +
	"\x06memory\x18\x02 \x01(\v2\r.global.UsageR\x06memory\x12\x1a\n" +
	"\bcpuUsage\x18\x03 \x01(\x02R\bcpuUsage\x12)\n" +
	"\bhardDisk\x18\x04 \x01(\v2\r.global.UsageR\bhardDisk\x12-\n" +
	"\anetWork\x18\x05 \x03(\v2\x13.global.NetWorkInfoR\anetWork\x12 \n" +
	"\vstreamCount\x18\x06 \x01(\x05R\vstreamCount\x12&\n" +
	"\x0esubscribeCount\x18\a \x01(\x05R\x0esubscribeCount\x12\x1c\n" +
	"\tpullCount\x18\b \x01(\x05R\tpullCount\x12\x1c\n" +
	"\tpushCount\x18\t \x01(\x05R\tpushCount\x12 \n" +
	"\vrecordCount\x18\n" +
	" \x01(\x05R\vrecordCount\x12&\n" +
	"\x0etransformCount\x18\v \x01(\x05R\x0etransformCount\"\xdf\x01\n" +
	"\n" +
	"PluginInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1a\n" +
	"\bpushAddr\x18\x02 \x03(\tR\bpushAddr\x12\x1a\n" +
	"\bplayAddr\x18\x03 \x03(\tR\bplayAddr\x12E\n" +
	"\vdescription\x18\x04 \x03(\v2#.global.PluginInfo.DescriptionEntryR\vdescription\x1a>\n" +
	"\x10DescriptionEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x9b\x02\n" +
	"\vSysInfoData\x128\n" +
	"\tstartTime\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12\x18\n" +
	"\alocalIP\x18\x02 \x01(\tR\alocalIP\x12\x1a\n" +
	"\bpublicIP\x18\x03 \x01(\tR\bpublicIP\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12\x1c\n" +
	"\tgoVersion\x18\x05 \x01(\tR\tgoVersion\x12\x0e\n" +
	"\x02os\x18\x06 \x01(\tR\x02os\x12\x12\n" +
	"\x04arch\x18\a \x01(\tR\x04arch\x12\x12\n" +
	"\x04cpus\x18\b \x01(\x05R\x04cpus\x12,\n" +
	"\aplugins\x18\t \x03(\v2\x12.global.PluginInfoR\aplugins\"h\n" +
	"\x0fSysInfoResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12'\n" +
	"\x04data\x18\x03 \x01(\v2\x13.global.SysInfoDataR\x04data\"\x81\x04\n" +
	"\fTaskTreeData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x12\n" +
	"\x04type\x18\x02 \x01(\rR\x04type\x12\x14\n" +
	"\x05owner\x18\x03 \x01(\tR\x05owner\x128\n" +
	"\tstartTime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12G\n" +
	"\vdescription\x18\x05 \x03(\v2%.global.TaskTreeData.DescriptionEntryR\vdescription\x120\n" +
	"\bchildren\x18\x06 \x03(\v2\x14.global.TaskTreeDataR\bchildren\x12\x14\n" +
	"\x05state\x18\a \x01(\rR\x05state\x12.\n" +
	"\ablocked\x18\b \x01(\v2\x14.global.TaskTreeDataR\ablocked\x12\x18\n" +
	"\apointer\x18\t \x01(\x04R\apointer\x12 \n" +
	"\vstartReason\x18\n" +
	" \x01(\tR\vstartReason\x12*\n" +
	"\x10eventLoopRunning\x18\v \x01(\bR\x10eventLoopRunning\x12\x14\n" +
	"\x05level\x18\f \x01(\rR\x05level\x1a>\n" +
	"\x10DescriptionEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"j\n" +
	"\x10TaskTreeResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12(\n" +
	"\x04data\x18\x03 \x01(\v2\x14.global.TaskTreeDataR\x04data\"I\n" +
	"\x11StreamListRequest\x12\x18\n" +
	"\apageNum\x18\x01 \x01(\x05R\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x02 \x01(\x05R\bpageSize\"\xb6\x01\n" +
	"\x12StreamListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12\x18\n" +
	"\apageNum\x18\x04 \x01(\x05R\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x05 \x01(\x05R\bpageSize\x12&\n" +
	"\x04data\x18\x06 \x03(\v2\x12.global.StreamInfoR\x04data\"\x8f\x01\n" +
	"\x16StreamWaitListResponse\x12<\n" +
	"\x04list\x18\x01 \x03(\v2(.global.StreamWaitListResponse.ListEntryR\x04list\x1a7\n" +
	"\tListEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\"3\n" +
	"\x11StreamSnapRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\"j\n" +
	"\x12StreamInfoResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x04data\x18\x03 \x01(\v2\x12.global.StreamInfoR\x04data\"\xa0\x04\n" +
	"\n" +
	"StreamInfo\x12\x12\n" +
	"\x04path\x18\x01 \x01(\tR\x04path\x12\x14\n" +
	"\x05state\x18\x02 \x01(\x05R\x05state\x12 \n" +
	"\vsubscribers\x18\x03 \x01(\x05R\vsubscribers\x126\n" +
	"\n" +
	"audioTrack\x18\x04 \x01(\v2\x16.global.AudioTrackInfoR\n" +
	"audioTrack\x126\n" +
	"\n" +
	"videoTrack\x18\x05 \x01(\v2\x16.global.VideoTrackInfoR\n" +
	"videoTrack\x128\n" +
	"\tstartTime\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12\x1e\n" +
	"\n" +
	"pluginName\x18\a \x01(\tR\n" +
	"pluginName\x12\x12\n" +
	"\x04type\x18\b \x01(\tR\x04type\x12\x12\n" +
	"\x04meta\x18\t \x01(\tR\x04meta\x12\x1a\n" +
	"\bisPaused\x18\n" +
	" \x01(\bR\bisPaused\x12\x10\n" +
	"\x03gop\x18\v \x01(\x05R\x03gop\x12\x14\n" +
	"\x05speed\x18\f \x01(\x02R\x05speed\x129\n" +
	"\n" +
	"bufferTime\x18\r \x01(\v2\x19.google.protobuf.DurationR\n" +
	"bufferTime\x12\x1e\n" +
	"\n" +
	"stopOnIdle\x18\x0e \x01(\bR\n" +
	"stopOnIdle\x125\n" +
	"\trecording\x18\x0f \x03(\v2\x17.global.RecordingDetailR\trecording\"\xca\x01\n" +
	"\x0fRecordingDetail\x12\x1a\n" +
	"\bfilePath\x18\x01 \x01(\tR\bfilePath\x12\x12\n" +
	"\x04mode\x18\x02 \x01(\tR\x04mode\x125\n" +
	"\bfragment\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\bfragment\x12\x16\n" +
	"\x06append\x18\x04 \x01(\bR\x06append\x12\x1e\n" +
	"\n" +
	"pluginName\x18\x05 \x01(\tR\n" +
	"pluginName\x12\x18\n" +
	"\apointer\x18\x06 \x01(\x04R\apointer\"L\n" +
	"\x04Wrap\x12\x1c\n" +
	"\ttimestamp\x18\x01 \x01(\rR\ttimestamp\x12\x12\n" +
	"\x04size\x18\x02 \x01(\rR\x04size\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\"\xc1\x01\n" +
	"\rTrackSnapShot\x12\x1a\n" +
	"\bsequence\x18\x01 \x01(\rR\bsequence\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\rR\ttimestamp\x128\n" +
	"\twriteTime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\twriteTime\x12\x1a\n" +
	"\bkeyFrame\x18\x04 \x01(\bR\bkeyFrame\x12 \n" +
	"\x04wrap\x18\x05 \x03(\v2\f.global.WrapR\x04wrap\")\n" +
	"\vMemoryBlock\x12\f\n" +
	"\x01s\x18\x01 \x01(\rR\x01s\x12\f\n" +
	"\x01e\x18\x02 \x01(\rR\x01e\"O\n" +
	"\x10MemoryBlockGroup\x12\x12\n" +
	"\x04size\x18\x01 \x01(\rR\x04size\x12'\n" +
	"\x04list\x18\x02 \x03(\v2\x13.global.MemoryBlockR\x04list\"\xc9\x01\n" +
	"\x0eAudioTrackInfo\x12\x14\n" +
	"\x05codec\x18\x01 \x01(\tR\x05codec\x12\x14\n" +
	"\x05delta\x18\x02 \x01(\tR\x05delta\x12\x12\n" +
	"\x04meta\x18\x03 \x01(\tR\x04meta\x12\x10\n" +
	"\x03bps\x18\x04 \x01(\rR\x03bps\x12\x17\n" +
	"\abps_out\x18\x05 \x01(\rR\x06bpsOut\x12\x10\n" +
	"\x03fps\x18\x06 \x01(\rR\x03fps\x12\x1e\n" +
	"\n" +
	"sampleRate\x18\a \x01(\rR\n" +
	"sampleRate\x12\x1a\n" +
	"\bchannels\x18\b \x01(\rR\bchannels\"\x8e\x02\n" +
	"\x11TrackSnapShotData\x12)\n" +
	"\x04ring\x18\x01 \x03(\v2\x15.global.TrackSnapShotR\x04ring\x12\"\n" +
	"\fringDataSize\x18\x02 \x01(\rR\fringDataSize\x12=\n" +
	"\x06reader\x18\x03 \x03(\v2%.global.TrackSnapShotData.ReaderEntryR\x06reader\x120\n" +
	"\x06memory\x18\x04 \x03(\v2\x18.global.MemoryBlockGroupR\x06memory\x1a9\n" +
	"\vReaderEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\rR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\rR\x05value:\x028\x01\"t\n" +
	"\x15TrackSnapShotResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12-\n" +
	"\x04data\x18\x03 \x01(\v2\x19.global.TrackSnapShotDataR\x04data\"\xcd\x01\n" +
	"\x0eVideoTrackInfo\x12\x14\n" +
	"\x05codec\x18\x01 \x01(\tR\x05codec\x12\x14\n" +
	"\x05delta\x18\x02 \x01(\tR\x05delta\x12\x12\n" +
	"\x04meta\x18\x03 \x01(\tR\x04meta\x12\x10\n" +
	"\x03bps\x18\x04 \x01(\rR\x03bps\x12\x17\n" +
	"\abps_out\x18\x05 \x01(\rR\x06bpsOut\x12\x10\n" +
	"\x03fps\x18\x06 \x01(\rR\x03fps\x12\x14\n" +
	"\x05width\x18\a \x01(\rR\x05width\x12\x16\n" +
	"\x06height\x18\b \x01(\rR\x06height\x12\x10\n" +
	"\x03gop\x18\t \x01(\rR\x03gop\"?\n" +
	"\x0fSuccessResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"?\n" +
	"\rRequestWithId\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x02 \x01(\tR\n" +
	"streamPath\"!\n" +
	"\x0fRequestWithId64\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\"H\n" +
	"\x16ChangeSubscribeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x02 \x01(\tR\n" +
	"streamPath\"j\n" +
	"\x12SubscribersRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x18\n" +
	"\apageNum\x18\x02 \x01(\x05R\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x03 \x01(\x05R\bpageSize\"\x8c\x01\n" +
	"\x12RingReaderSnapShot\x12\x1a\n" +
	"\bsequence\x18\x01 \x01(\rR\bsequence\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\rR\ttimestamp\x12\x14\n" +
	"\x05delay\x18\x03 \x01(\rR\x05delay\x12\x14\n" +
	"\x05state\x18\x04 \x01(\x05R\x05state\x12\x10\n" +
	"\x03bps\x18\x05 \x01(\rR\x03bps\"\xb3\x03\n" +
	"\x12SubscriberSnapShot\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x128\n" +
	"\tstartTime\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12<\n" +
	"\vaudioReader\x18\x03 \x01(\v2\x1a.global.RingReaderSnapShotR\vaudioReader\x12<\n" +
	"\vvideoReader\x18\x04 \x01(\v2\x1a.global.RingReaderSnapShotR\vvideoReader\x12\x12\n" +
	"\x04meta\x18\x05 \x01(\tR\x04meta\x129\n" +
	"\n" +
	"bufferTime\x18\x06 \x01(\v2\x19.google.protobuf.DurationR\n" +
	"bufferTime\x12\x18\n" +
	"\asubMode\x18\a \x01(\x05R\asubMode\x12\x1a\n" +
	"\bsyncMode\x18\b \x01(\x05R\bsyncMode\x12\x1e\n" +
	"\n" +
	"pluginName\x18\t \x01(\tR\n" +
	"pluginName\x12\x12\n" +
	"\x04type\x18\n" +
	" \x01(\tR\x04type\x12\x1e\n" +
	"\n" +
	"remoteAddr\x18\v \x01(\tR\n" +
	"remoteAddr\"\xbf\x01\n" +
	"\x13SubscribersResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12\x18\n" +
	"\apageNum\x18\x04 \x01(\x05R\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x05 \x01(\x05R\bpageSize\x12.\n" +
	"\x04data\x18\x06 \x03(\v2\x1a.global.SubscriberSnapShotR\x04data\"p\n" +
	"\x15PullProxyListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12)\n" +
	"\x04data\x18\x03 \x03(\v2\x15.global.PullProxyInfoR\x04data\"\xdd\x04\n" +
	"\rPullProxyInfo\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\rR\x02ID\x12:\n" +
	"\n" +
	"createTime\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12:\n" +
	"\n" +
	"updateTime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1a\n" +
	"\bparentID\x18\x04 \x01(\rR\bparentID\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x06 \x01(\tR\x04type\x12\x16\n" +
	"\x06status\x18\a \x01(\rR\x06status\x12\x18\n" +
	"\apullURL\x18\b \x01(\tR\apullURL\x12 \n" +
	"\vpullOnStart\x18\t \x01(\bR\vpullOnStart\x12\x1e\n" +
	"\n" +
	"stopOnIdle\x18\n" +
	" \x01(\bR\n" +
	"stopOnIdle\x12\x14\n" +
	"\x05audio\x18\v \x01(\bR\x05audio\x12 \n" +
	"\vdescription\x18\f \x01(\tR\vdescription\x12\x1e\n" +
	"\n" +
	"recordPath\x18\r \x01(\tR\n" +
	"recordPath\x12A\n" +
	"\x0erecordFragment\x18\x0e \x01(\v2\x19.google.protobuf.DurationR\x0erecordFragment\x12\x10\n" +
	"\x03rtt\x18\x0f \x01(\rR\x03rtt\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x10 \x01(\tR\n" +
	"streamPath\x12?\n" +
	"\rcheckInterval\x18\x11 \x01(\v2\x19.google.protobuf.DurationR\rcheckInterval\"\xcf\x05\n" +
	"\x16UpdatePullProxyRequest\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\rR\x02ID\x12\x1f\n" +
	"\bparentID\x18\x02 \x01(\rH\x00R\bparentID\x88\x01\x01\x12\x17\n" +
	"\x04name\x18\x03 \x01(\tH\x01R\x04name\x88\x01\x01\x12\x17\n" +
	"\x04type\x18\x04 \x01(\tH\x02R\x04type\x88\x01\x01\x12\x1b\n" +
	"\x06status\x18\x05 \x01(\rH\x03R\x06status\x88\x01\x01\x12\x1d\n" +
	"\apullURL\x18\x06 \x01(\tH\x04R\apullURL\x88\x01\x01\x12%\n" +
	"\vpullOnStart\x18\a \x01(\bH\x05R\vpullOnStart\x88\x01\x01\x12#\n" +
	"\n" +
	"stopOnIdle\x18\b \x01(\bH\x06R\n" +
	"stopOnIdle\x88\x01\x01\x12\x19\n" +
	"\x05audio\x18\t \x01(\bH\aR\x05audio\x88\x01\x01\x12%\n" +
	"\vdescription\x18\n" +
	" \x01(\tH\bR\vdescription\x88\x01\x01\x12#\n" +
	"\n" +
	"recordPath\x18\v \x01(\tH\tR\n" +
	"recordPath\x88\x01\x01\x12F\n" +
	"\x0erecordFragment\x18\f \x01(\v2\x19.google.protobuf.DurationH\n" +
	"R\x0erecordFragment\x88\x01\x01\x12#\n" +
	"\n" +
	"streamPath\x18\r \x01(\tH\vR\n" +
	"streamPath\x88\x01\x01\x12D\n" +
	"\rcheckInterval\x18\x0e \x01(\v2\x19.google.protobuf.DurationH\fR\rcheckInterval\x88\x01\x01B\v\n" +
	"\t_parentIDB\a\n" +
	"\x05_nameB\a\n" +
	"\x05_typeB\t\n" +
	"\a_statusB\n" +
	"\n" +
	"\b_pullURLB\x0e\n" +
	"\f_pullOnStartB\r\n" +
	"\v_stopOnIdleB\b\n" +
	"\x06_audioB\x0e\n" +
	"\f_descriptionB\r\n" +
	"\v_recordPathB\x11\n" +
	"\x0f_recordFragmentB\r\n" +
	"\v_streamPathB\x10\n" +
	"\x0e_checkInterval\"\x99\x03\n" +
	"\rPushProxyInfo\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\rR\x02ID\x12:\n" +
	"\n" +
	"createTime\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12:\n" +
	"\n" +
	"updateTime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1a\n" +
	"\bparentID\x18\x04 \x01(\rR\bparentID\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x06 \x01(\tR\x04type\x12\x16\n" +
	"\x06status\x18\a \x01(\rR\x06status\x12\x18\n" +
	"\apushURL\x18\b \x01(\tR\apushURL\x12 \n" +
	"\vpushOnStart\x18\t \x01(\bR\vpushOnStart\x12\x14\n" +
	"\x05audio\x18\n" +
	" \x01(\bR\x05audio\x12 \n" +
	"\vdescription\x18\v \x01(\tR\vdescription\x12\x10\n" +
	"\x03rtt\x18\f \x01(\rR\x03rtt\x12\x1e\n" +
	"\n" +
	"streamPath\x18\r \x01(\tR\n" +
	"streamPath\"\xd3\x03\n" +
	"\x16UpdatePushProxyRequest\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\rR\x02ID\x12\x1f\n" +
	"\bparentID\x18\x02 \x01(\rH\x00R\bparentID\x88\x01\x01\x12\x17\n" +
	"\x04name\x18\x03 \x01(\tH\x01R\x04name\x88\x01\x01\x12\x17\n" +
	"\x04type\x18\x04 \x01(\tH\x02R\x04type\x88\x01\x01\x12\x1b\n" +
	"\x06status\x18\x05 \x01(\rH\x03R\x06status\x88\x01\x01\x12\x1d\n" +
	"\apushURL\x18\x06 \x01(\tH\x04R\apushURL\x88\x01\x01\x12%\n" +
	"\vpushOnStart\x18\a \x01(\bH\x05R\vpushOnStart\x88\x01\x01\x12\x19\n" +
	"\x05audio\x18\b \x01(\bH\x06R\x05audio\x88\x01\x01\x12%\n" +
	"\vdescription\x18\t \x01(\tH\aR\vdescription\x88\x01\x01\x12\x15\n" +
	"\x03rtt\x18\n" +
	" \x01(\rH\bR\x03rtt\x88\x01\x01\x12#\n" +
	"\n" +
	"streamPath\x18\v \x01(\tH\tR\n" +
	"streamPath\x88\x01\x01B\v\n" +
	"\t_parentIDB\a\n" +
	"\x05_nameB\a\n" +
	"\x05_typeB\t\n" +
	"\a_statusB\n" +
	"\n" +
	"\b_pushURLB\x0e\n" +
	"\f_pushOnStartB\b\n" +
	"\x06_audioB\x0e\n" +
	"\f_descriptionB\x06\n" +
	"\x04_rttB\r\n" +
	"\v_streamPath\"p\n" +
	"\x15PushProxyListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12)\n" +
	"\x04data\x18\x03 \x03(\v2\x15.global.PushProxyInfoR\x04data\"m\n" +
	"\x15SetStreamAliasRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x14\n" +
	"\x05alias\x18\x02 \x01(\tR\x05alias\x12\x1e\n" +
	"\n" +
	"autoRemove\x18\x03 \x01(\bR\n" +
	"autoRemove\"{\n" +
	"\vStreamAlias\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x14\n" +
	"\x05alias\x18\x02 \x01(\tR\x05alias\x12\x1e\n" +
	"\n" +
	"autoRemove\x18\x03 \x01(\bR\n" +
	"autoRemove\x12\x16\n" +
	"\x06status\x18\x04 \x01(\rR\x06status\"p\n" +
	"\x17StreamAliasListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12'\n" +
	"\x04data\x18\x03 \x03(\v2\x13.global.StreamAliasR\x04data\"M\n" +
	"\x15SetStreamSpeedRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x14\n" +
	"\x05speed\x18\x02 \x01(\x02R\x05speed\"Q\n" +
	"\x11SeekStreamRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x1c\n" +
	"\ttimeStamp\x18\x02 \x01(\rR\ttimeStamp\"\x93\x01\n" +
	"\tRecording\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x128\n" +
	"\tstartTime\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x18\n" +
	"\apointer\x18\x04 \x01(\x04R\apointer\"l\n" +
	"\x15RecordingListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x04data\x18\x03 \x03(\v2\x11.global.RecordingR\x04data\"\x9a\x01\n" +
	"\bPushInfo\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x1c\n" +
	"\ttargetURL\x18\x02 \x01(\tR\ttargetURL\x128\n" +
	"\tstartTime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\"f\n" +
	"\x10PushListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12$\n" +
	"\x04data\x18\x03 \x03(\v2\x10.global.PushInfoR\x04data\"N\n" +
	"\x0eAddPushRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x1c\n" +
	"\ttargetURL\x18\x02 \x01(\tR\ttargetURL\"{\n" +
	"\tTransform\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x16\n" +
	"\x06target\x18\x02 \x01(\tR\x06target\x12\x1e\n" +
	"\n" +
	"pluginName\x18\x03 \x01(\tR\n" +
	"pluginName\x12\x16\n" +
	"\x06config\x18\x04 \x01(\tR\x06config\"l\n" +
	"\x15TransformListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x04data\x18\x03 \x03(\v2\x11.global.TransformR\x04data\"\xd7\x01\n" +
	"\rReqRecordList\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x14\n" +
	"\x05range\x18\x02 \x01(\tR\x05range\x12\x14\n" +
	"\x05start\x18\x03 \x01(\tR\x05start\x12\x10\n" +
	"\x03end\x18\x04 \x01(\tR\x03end\x12\x18\n" +
	"\apageNum\x18\x05 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x06 \x01(\rR\bpageSize\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\x12\x1e\n" +
	"\n" +
	"eventLevel\x18\b \x01(\tR\n" +
	"eventLevel\"\xc8\x01\n" +
	"\n" +
	"RecordFile\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1a\n" +
	"\bfilePath\x18\x02 \x01(\tR\bfilePath\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x03 \x01(\tR\n" +
	"streamPath\x128\n" +
	"\tstartTime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x124\n" +
	"\aendTime\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\"\xc3\x02\n" +
	"\x0fEventRecordFile\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1a\n" +
	"\bfilePath\x18\x02 \x01(\tR\bfilePath\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x03 \x01(\tR\n" +
	"streamPath\x128\n" +
	"\tstartTime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x124\n" +
	"\aendTime\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x18\n" +
	"\aeventId\x18\x06 \x01(\tR\aeventId\x12\x1e\n" +
	"\n" +
	"eventLevel\x18\a \x01(\tR\n" +
	"eventLevel\x12\x1c\n" +
	"\teventName\x18\b \x01(\tR\teventName\x12\x1c\n" +
	"\teventDesc\x18\t \x01(\tR\teventDesc\"\xb6\x01\n" +
	"\x12RecordResponseList\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\rR\x05total\x12\x18\n" +
	"\apageNum\x18\x04 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x05 \x01(\rR\bpageSize\x12&\n" +
	"\x04data\x18\x06 \x03(\v2\x12.global.RecordFileR\x04data\"\xc0\x01\n" +
	"\x17EventRecordResponseList\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\rR\x05total\x12\x18\n" +
	"\apageNum\x18\x04 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x05 \x01(\rR\bpageSize\x12+\n" +
	"\x04data\x18\x06 \x03(\v2\x17.global.EventRecordFileR\x04data\"\xaf\x01\n" +
	"\aCatalog\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x14\n" +
	"\x05count\x18\x02 \x01(\rR\x05count\x128\n" +
	"\tstartTime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x124\n" +
	"\aendTime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\"d\n" +
	"\x0fResponseCatalog\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12#\n" +
	"\x04data\x18\x03 \x03(\v2\x0f.global.CatalogR\x04data\"\xa5\x01\n" +
	"\x0fReqRecordDelete\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x10\n" +
	"\x03ids\x18\x02 \x03(\rR\x03ids\x12\x1c\n" +
	"\tstartTime\x18\x03 \x01(\tR\tstartTime\x12\x18\n" +
	"\aendTime\x18\x04 \x01(\tR\aendTime\x12\x14\n" +
	"\x05range\x18\x05 \x01(\tR\x05range\x12\x12\n" +
	"\x04type\x18\x06 \x01(\tR\x04type\"f\n" +
	"\x0eResponseDelete\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x04data\x18\x03 \x03(\v2\x12.global.RecordFileR\x04data\"&\n" +
	"\x10ReqRecordCatalog\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\"\xfd\x02\n" +
	"\tAlarmInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1e\n" +
	"\n" +
	"serverInfo\x18\x02 \x01(\tR\n" +
	"serverInfo\x12\x1e\n" +
	"\n" +
	"streamName\x18\x03 \x01(\tR\n" +
	"streamName\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x04 \x01(\tR\n" +
	"streamPath\x12\x1c\n" +
	"\talarmDesc\x18\x05 \x01(\tR\talarmDesc\x12\x1c\n" +
	"\talarmName\x18\x06 \x01(\tR\talarmName\x12\x1c\n" +
	"\talarmType\x18\a \x01(\x05R\talarmType\x12\x16\n" +
	"\x06isSent\x18\b \x01(\bR\x06isSent\x12\x1a\n" +
	"\bfilePath\x18\t \x01(\tR\bfilePath\x128\n" +
	"\tcreatedAt\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x128\n" +
	"\tupdatedAt\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xe4\x01\n" +
	"\x10AlarmListRequest\x12\x18\n" +
	"\apageNum\x18\x01 \x01(\x05R\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x02 \x01(\x05R\bpageSize\x12\x14\n" +
	"\x05range\x18\x03 \x01(\tR\x05range\x12\x14\n" +
	"\x05start\x18\x04 \x01(\tR\x05start\x12\x10\n" +
	"\x03end\x18\x05 \x01(\tR\x03end\x12\x1c\n" +
	"\talarmType\x18\x06 \x01(\x05R\talarmType\x12\x1e\n" +
	"\n" +
	"streamPath\x18\a \x01(\tR\n" +
	"streamPath\x12\x1e\n" +
	"\n" +
	"streamName\x18\b \x01(\tR\n" +
	"streamName\"\xb4\x01\n" +
	"\x11AlarmListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12\x18\n" +
	"\apageNum\x18\x04 \x01(\x05R\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x05 \x01(\x05R\bpageSize\x12%\n" +
	"\x04data\x18\x06 \x03(\v2\x11.global.AlarmInfoR\x04data\"\xca\x01\n" +
	"\x04Step\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\x128\n" +
	"\tstartedAt\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x12<\n" +
	"\vcompletedAt\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\vcompletedAt\"`\n" +
	"\x18SubscriptionProgressData\x12\"\n" +
	"\x05steps\x18\x01 \x03(\v2\f.global.StepR\x05steps\x12 \n" +
	"\vcurrentStep\x18\x02 \x01(\x05R\vcurrentStep\"\x82\x01\n" +
	"\x1cSubscriptionProgressResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x124\n" +
	"\x04data\x18\x03 \x01(\v2 .global.SubscriptionProgressDataR\x04data\"\xe9\b\n" +
	"\x11GlobalPullRequest\x12\x1c\n" +
	"\tremoteURL\x18\x01 \x01(\tR\tremoteURL\x12\x1a\n" +
	"\bprotocol\x18\x02 \x01(\tR\bprotocol\x12\x1a\n" +
	"\btestMode\x18\x03 \x01(\x05R\btestMode\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x04 \x01(\tR\n" +
	"streamPath\x12\x1f\n" +
	"\bpubAudio\x18\x05 \x01(\bH\x00R\bpubAudio\x88\x01\x01\x12\x1f\n" +
	"\bpubVideo\x18\x06 \x01(\bH\x01R\bpubVideo\x88\x01\x01\x12L\n" +
	"\x11delayCloseTimeout\x18\a \x01(\v2\x19.google.protobuf.DurationH\x02R\x11delayCloseTimeout\x88\x01\x01\x12\x19\n" +
	"\x05speed\x18\b \x01(\x01H\x03R\x05speed\x88\x01\x01\x12\x1f\n" +
	"\bmaxCount\x18\t \x01(\x05H\x04R\bmaxCount\x88\x01\x01\x12!\n" +
	"\tkickExist\x18\n" +
	" \x01(\bH\x05R\tkickExist\x88\x01\x01\x12F\n" +
	"\x0epublishTimeout\x18\v \x01(\v2\x19.google.protobuf.DurationH\x06R\x0epublishTimeout\x88\x01\x01\x12J\n" +
	"\x10waitCloseTimeout\x18\f \x01(\v2\x19.google.protobuf.DurationH\aR\x10waitCloseTimeout\x88\x01\x01\x12@\n" +
	"\vidleTimeout\x18\r \x01(\v2\x19.google.protobuf.DurationH\bR\vidleTimeout\x88\x01\x01\x12B\n" +
	"\fpauseTimeout\x18\x0e \x01(\v2\x19.google.protobuf.DurationH\tR\fpauseTimeout\x88\x01\x01\x12>\n" +
	"\n" +
	"bufferTime\x18\x0f \x01(\v2\x19.google.protobuf.DurationH\n" +
	"R\n" +
	"bufferTime\x88\x01\x01\x12\x19\n" +
	"\x05scale\x18\x10 \x01(\x01H\vR\x05scale\x88\x01\x01\x12\x1b\n" +
	"\x06maxFPS\x18\x11 \x01(\x05H\fR\x06maxFPS\x88\x01\x01\x12\x15\n" +
	"\x03key\x18\x12 \x01(\tH\rR\x03key\x88\x01\x01\x12!\n" +
	"\trelayMode\x18\x13 \x01(\tH\x0eR\trelayMode\x88\x01\x01\x12\x1d\n" +
	"\apubType\x18\x14 \x01(\tH\x0fR\apubType\x88\x01\x01\x12\x17\n" +
	"\x04dump\x18\x15 \x01(\bH\x10R\x04dump\x88\x01\x01B\v\n" +
	"\t_pubAudioB\v\n" +
	"\t_pubVideoB\x14\n" +
	"\x12_delayCloseTimeoutB\b\n" +
	"\x06_speedB\v\n" +
	"\t_maxCountB\f\n" +
	"\n" +
	"_kickExistB\x11\n" +
	"\x0f_publishTimeoutB\x13\n" +
	"\x11_waitCloseTimeoutB\x0e\n" +
	"\f_idleTimeoutB\x0f\n" +
	"\r_pauseTimeoutB\r\n" +
	"\v_bufferTimeB\b\n" +
	"\x06_scaleB\t\n" +
	"\a_maxFPSB\x06\n" +
	"\x04_keyB\f\n" +
	"\n" +
	"_relayModeB\n" +
	"\n" +
	"\b_pubTypeB\a\n" +
	"\x05_dump2\x94%\n" +
	"\x03api\x12P\n" +
	"\aSysInfo\x12\x16.google.protobuf.Empty\x1a\x17.global.SysInfoResponse\"\x14\x82\xd3\xe4\x93\x02\x0e\x12\f/api/sysinfo\x12i\n" +
	"\x0fDisabledPlugins\x12\x16.google.protobuf.Empty\x1a\x1f.global.DisabledPluginsResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/api/plugins/disabled\x12P\n" +
	"\aSummary\x12\x16.google.protobuf.Empty\x1a\x17.global.SummaryResponse\"\x14\x82\xd3\xe4\x93\x02\x0e\x12\f/api/summary\x12Q\n" +
	"\bShutdown\x12\x15.global.RequestWithId\x1a\x17.global.SuccessResponse\"\x15\x82\xd3\xe4\x93\x02\x0f\"\r/api/shutdown\x12O\n" +
	"\aRestart\x12\x15.global.RequestWithId\x1a\x17.global.SuccessResponse\"\x14\x82\xd3\xe4\x93\x02\x0e\"\f/api/restart\x12T\n" +
	"\bTaskTree\x12\x16.google.protobuf.Empty\x1a\x18.global.TaskTreeResponse\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/api/task/tree\x12Y\n" +
	"\bStopTask\x12\x17.global.RequestWithId64\x1a\x17.global.SuccessResponse\"\x1b\x82\xd3\xe4\x93\x02\x15\"\x13/api/task/stop/{id}\x12_\n" +
	"\vRestartTask\x12\x17.global.RequestWithId64\x1a\x17.global.SuccessResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\"\x16/api/task/restart/{id}\x12]\n" +
	"\n" +
	"StreamList\x12\x19.global.StreamListRequest\x1a\x1a.global.StreamListResponse\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/api/stream/list\x12`\n" +
	"\bWaitList\x12\x16.google.protobuf.Empty\x1a\x1e.global.StreamWaitListResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/api/stream/waitlist\x12m\n" +
	"\n" +
	"StreamInfo\x12\x19.global.StreamSnapRequest\x1a\x1a.global.StreamInfoResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /api/stream/info/{streamPath=**}\x12o\n" +
	"\vPauseStream\x12\x19.global.StreamSnapRequest\x1a\x17.global.SuccessResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/api/stream/pause/{streamPath=**}\x12q\n" +
	"\fResumeStream\x12\x19.global.StreamSnapRequest\x1a\x17.global.SuccessResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/api/stream/resume/{streamPath=**}\x12v\n" +
	"\x0eSetStreamSpeed\x12\x1d.global.SetStreamSpeedRequest\x1a\x17.global.SuccessResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/api/stream/speed/{streamPath=**}\x12m\n" +
	"\n" +
	"SeekStream\x12\x19.global.SeekStreamRequest\x1a\x17.global.SuccessResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /api/stream/seek/{streamPath=**}\x12s\n" +
	"\x0eGetSubscribers\x12\x1a.global.SubscribersRequest\x1a\x1b.global.SubscribersResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /api/subscribers/{streamPath=**}\x12x\n" +
	"\x0eAudioTrackSnap\x12\x19.global.StreamSnapRequest\x1a\x1d.global.TrackSnapShotResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/audiotrack/snap/{streamPath=**}\x12x\n" +
	"\x0eVideoTrackSnap\x12\x19.global.StreamSnapRequest\x1a\x1d.global.TrackSnapShotResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/videotrack/snap/{streamPath=**}\x12\x81\x01\n" +
	"\x0fChangeSubscribe\x12\x1e.global.ChangeSubscribeRequest\x1a\x17.global.SuccessResponse\"5\x82\xd3\xe4\x93\x02/:\x01*\"*/api/subscribe/change/{id}/{streamPath=**}\x12i\n" +
	"\x0eGetStreamAlias\x12\x16.google.protobuf.Empty\x1a\x1f.global.StreamAliasListResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/stream/alias/list\x12f\n" +
	"\x0eSetStreamAlias\x12\x1d.global.SetStreamAliasRequest\x1a\x17.global.SuccessResponse\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/api/stream/alias\x12n\n" +
	"\vStopPublish\x12\x19.global.StreamSnapRequest\x1a\x17.global.SuccessResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /api/stream/stop/{streamPath=**}\x12d\n" +
	"\rStopSubscribe\x12\x15.global.RequestWithId\x1a\x17.global.SuccessResponse\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/api/subscribe/stop/{id}\x12`\n" +
	"\rGetConfigFile\x12\x16.google.protobuf.Empty\x1a\x1d.global.GetConfigFileResponse\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/api/config/file\x12v\n" +
	"\x10UpdateConfigFile\x12\x1f.global.UpdateConfigFileRequest\x1a\x17.global.SuccessResponse\"(\x82\xd3\xe4\x93\x02\":\acontent\"\x17/api/config/file/update\x12`\n" +
	"\tGetConfig\x12\x18.global.GetConfigRequest\x1a\x19.global.GetConfigResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/config/get/{name}\x12e\n" +
	"\n" +
	"GetFormily\x12\x18.global.GetConfigRequest\x1a\x19.global.GetConfigResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/api/config/formily/{name}\x12{\n" +
	"\x10GetPullProxyList\x12\x16.google.protobuf.Empty\x1a\x1d.global.PullProxyListResponse\"0\x82\xd3\xe4\x93\x02*Z\x12\x12\x10/api/device/list\x12\x14/api/proxy/pull/list\x12t\n" +
	"\fAddPullProxy\x12\x15.global.PullProxyInfo\x1a\x17.global.SuccessResponse\"4\x82\xd3\xe4\x93\x02.:\x01*Z\x14:\x01*\"\x0f/api/device/add\"\x13/api/proxy/pull/add\x12\x87\x01\n" +
	"\x0fRemovePullProxy\x12\x15.global.RequestWithId\x1a\x17.global.SuccessResponse\"D\x82\xd3\xe4\x93\x02>:\x01*Z\x1c:\x01*\"\x17/api/device/remove/{id}\"\x1b/api/proxy/pull/remove/{id}\x12\x86\x01\n" +
	"\x0fUpdatePullProxy\x12\x1e.global.UpdatePullProxyRequest\x1a\x17.global.SuccessResponse\":\x82\xd3\xe4\x93\x024:\x01*Z\x17:\x01*\"\x12/api/device/update\"\x16/api/proxy/pull/update\x12g\n" +
	"\x10GetPushProxyList\x12\x16.google.protobuf.Empty\x1a\x1d.global.PushProxyListResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/api/proxy/push/list\x12^\n" +
	"\fAddPushProxy\x12\x15.global.PushProxyInfo\x1a\x17.global.SuccessResponse\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/api/proxy/push/add\x12i\n" +
	"\x0fRemovePushProxy\x12\x15.global.RequestWithId\x1a\x17.global.SuccessResponse\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/api/proxy/push/remove/{id}\x12m\n" +
	"\x0fUpdatePushProxy\x12\x1e.global.UpdatePushProxyRequest\x1a\x17.global.SuccessResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/proxy/push/update\x12_\n" +
	"\fGetRecording\x12\x16.google.protobuf.Empty\x1a\x1d.global.RecordingListResponse\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/api/record/list\x12f\n" +
	"\x10GetTransformList\x12\x16.google.protobuf.Empty\x1a\x1d.global.TransformListResponse\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/api/transform/list\x12s\n" +
	"\rGetRecordList\x12\x15.global.ReqRecordList\x1a\x1a.global.RecordResponseList\"/\x82\xd3\xe4\x93\x02)\x12'/api/record/{type}/list/{streamPath=**}\x12\x83\x01\n" +
	"\x12GetEventRecordList\x12\x15.global.ReqRecordList\x1a\x1f.global.EventRecordResponseList\"5\x82\xd3\xe4\x93\x02/\x12-/api/record/{type}/event/list/{streamPath=**}\x12i\n" +
	"\x10GetRecordCatalog\x12\x18.global.ReqRecordCatalog\x1a\x17.global.ResponseCatalog\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/api/record/{type}/catalog\x12u\n" +
	"\fDeleteRecord\x12\x17.global.ReqRecordDelete\x1a\x16.global.ResponseDelete\"4\x82\xd3\xe4\x93\x02.:\x01*\")/api/record/{type}/delete/{streamPath=**}\x12\\\n" +
	"\fGetAlarmList\x12\x18.global.AlarmListRequest\x1a\x19.global.AlarmListResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/api/alarm/list\x12\x88\x01\n" +
	"\x17GetSubscriptionProgress\x12\x19.global.StreamSnapRequest\x1a$.global.SubscriptionProgressResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/stream/progress/{streamPath=**}\x12\\\n" +
	"\tStartPull\x12\x19.global.GlobalPullRequest\x1a\x17.global.SuccessResponse\"\x1b\x82\xd3\xe4\x93\x02\x15:\x01*\"\x10/api/stream/pullB\x10Z\x0em7s.live/v5/pbb\x06proto3"

var (
	file_global_proto_rawDescOnce sync.Once
	file_global_proto_rawDescData []byte
)

func file_global_proto_rawDescGZIP() []byte {
	file_global_proto_rawDescOnce.Do(func() {
		file_global_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_global_proto_rawDesc), len(file_global_proto_rawDesc)))
	})
	return file_global_proto_rawDescData
}

var file_global_proto_msgTypes = make([]protoimpl.MessageInfo, 82)
var file_global_proto_goTypes = []any{
	(*DisabledPluginsResponse)(nil),      // 0: global.DisabledPluginsResponse
	(*GetConfigRequest)(nil),             // 1: global.GetConfigRequest
	(*Formily)(nil),                      // 2: global.Formily
	(*FormilyResponse)(nil),              // 3: global.FormilyResponse
	(*ConfigData)(nil),                   // 4: global.ConfigData
	(*GetConfigFileResponse)(nil),        // 5: global.GetConfigFileResponse
	(*GetConfigResponse)(nil),            // 6: global.GetConfigResponse
	(*UpdateConfigFileRequest)(nil),      // 7: global.UpdateConfigFileRequest
	(*ModifyConfigRequest)(nil),          // 8: global.ModifyConfigRequest
	(*NetWorkInfo)(nil),                  // 9: global.NetWorkInfo
	(*Usage)(nil),                        // 10: global.Usage
	(*SummaryResponse)(nil),              // 11: global.SummaryResponse
	(*PluginInfo)(nil),                   // 12: global.PluginInfo
	(*SysInfoData)(nil),                  // 13: global.SysInfoData
	(*SysInfoResponse)(nil),              // 14: global.SysInfoResponse
	(*TaskTreeData)(nil),                 // 15: global.TaskTreeData
	(*TaskTreeResponse)(nil),             // 16: global.TaskTreeResponse
	(*StreamListRequest)(nil),            // 17: global.StreamListRequest
	(*StreamListResponse)(nil),           // 18: global.StreamListResponse
	(*StreamWaitListResponse)(nil),       // 19: global.StreamWaitListResponse
	(*StreamSnapRequest)(nil),            // 20: global.StreamSnapRequest
	(*StreamInfoResponse)(nil),           // 21: global.StreamInfoResponse
	(*StreamInfo)(nil),                   // 22: global.StreamInfo
	(*RecordingDetail)(nil),              // 23: global.RecordingDetail
	(*Wrap)(nil),                         // 24: global.Wrap
	(*TrackSnapShot)(nil),                // 25: global.TrackSnapShot
	(*MemoryBlock)(nil),                  // 26: global.MemoryBlock
	(*MemoryBlockGroup)(nil),             // 27: global.MemoryBlockGroup
	(*AudioTrackInfo)(nil),               // 28: global.AudioTrackInfo
	(*TrackSnapShotData)(nil),            // 29: global.TrackSnapShotData
	(*TrackSnapShotResponse)(nil),        // 30: global.TrackSnapShotResponse
	(*VideoTrackInfo)(nil),               // 31: global.VideoTrackInfo
	(*SuccessResponse)(nil),              // 32: global.SuccessResponse
	(*RequestWithId)(nil),                // 33: global.RequestWithId
	(*RequestWithId64)(nil),              // 34: global.RequestWithId64
	(*ChangeSubscribeRequest)(nil),       // 35: global.ChangeSubscribeRequest
	(*SubscribersRequest)(nil),           // 36: global.SubscribersRequest
	(*RingReaderSnapShot)(nil),           // 37: global.RingReaderSnapShot
	(*SubscriberSnapShot)(nil),           // 38: global.SubscriberSnapShot
	(*SubscribersResponse)(nil),          // 39: global.SubscribersResponse
	(*PullProxyListResponse)(nil),        // 40: global.PullProxyListResponse
	(*PullProxyInfo)(nil),                // 41: global.PullProxyInfo
	(*UpdatePullProxyRequest)(nil),       // 42: global.UpdatePullProxyRequest
	(*PushProxyInfo)(nil),                // 43: global.PushProxyInfo
	(*UpdatePushProxyRequest)(nil),       // 44: global.UpdatePushProxyRequest
	(*PushProxyListResponse)(nil),        // 45: global.PushProxyListResponse
	(*SetStreamAliasRequest)(nil),        // 46: global.SetStreamAliasRequest
	(*StreamAlias)(nil),                  // 47: global.StreamAlias
	(*StreamAliasListResponse)(nil),      // 48: global.StreamAliasListResponse
	(*SetStreamSpeedRequest)(nil),        // 49: global.SetStreamSpeedRequest
	(*SeekStreamRequest)(nil),            // 50: global.SeekStreamRequest
	(*Recording)(nil),                    // 51: global.Recording
	(*RecordingListResponse)(nil),        // 52: global.RecordingListResponse
	(*PushInfo)(nil),                     // 53: global.PushInfo
	(*PushListResponse)(nil),             // 54: global.PushListResponse
	(*AddPushRequest)(nil),               // 55: global.AddPushRequest
	(*Transform)(nil),                    // 56: global.Transform
	(*TransformListResponse)(nil),        // 57: global.TransformListResponse
	(*ReqRecordList)(nil),                // 58: global.ReqRecordList
	(*RecordFile)(nil),                   // 59: global.RecordFile
	(*EventRecordFile)(nil),              // 60: global.EventRecordFile
	(*RecordResponseList)(nil),           // 61: global.RecordResponseList
	(*EventRecordResponseList)(nil),      // 62: global.EventRecordResponseList
	(*Catalog)(nil),                      // 63: global.Catalog
	(*ResponseCatalog)(nil),              // 64: global.ResponseCatalog
	(*ReqRecordDelete)(nil),              // 65: global.ReqRecordDelete
	(*ResponseDelete)(nil),               // 66: global.ResponseDelete
	(*ReqRecordCatalog)(nil),             // 67: global.ReqRecordCatalog
	(*AlarmInfo)(nil),                    // 68: global.AlarmInfo
	(*AlarmListRequest)(nil),             // 69: global.AlarmListRequest
	(*AlarmListResponse)(nil),            // 70: global.AlarmListResponse
	(*Step)(nil),                         // 71: global.Step
	(*SubscriptionProgressData)(nil),     // 72: global.SubscriptionProgressData
	(*SubscriptionProgressResponse)(nil), // 73: global.SubscriptionProgressResponse
	(*GlobalPullRequest)(nil),            // 74: global.GlobalPullRequest
	nil,                                  // 75: global.Formily.PropertiesEntry
	nil,                                  // 76: global.Formily.ComponentPropsEntry
	nil,                                  // 77: global.FormilyResponse.PropertiesEntry
	nil,                                  // 78: global.PluginInfo.DescriptionEntry
	nil,                                  // 79: global.TaskTreeData.DescriptionEntry
	nil,                                  // 80: global.StreamWaitListResponse.ListEntry
	nil,                                  // 81: global.TrackSnapShotData.ReaderEntry
	(*timestamppb.Timestamp)(nil),        // 82: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),          // 83: google.protobuf.Duration
	(*anypb.Any)(nil),                    // 84: google.protobuf.Any
	(*emptypb.Empty)(nil),                // 85: google.protobuf.Empty
}
var file_global_proto_depIdxs = []int32{
	12,  // 0: global.DisabledPluginsResponse.data:type_name -> global.PluginInfo
	75,  // 1: global.Formily.properties:type_name -> global.Formily.PropertiesEntry
	76,  // 2: global.Formily.componentProps:type_name -> global.Formily.ComponentPropsEntry
	77,  // 3: global.FormilyResponse.properties:type_name -> global.FormilyResponse.PropertiesEntry
	4,   // 4: global.GetConfigResponse.data:type_name -> global.ConfigData
	10,  // 5: global.SummaryResponse.memory:type_name -> global.Usage
	10,  // 6: global.SummaryResponse.hardDisk:type_name -> global.Usage
	9,   // 7: global.SummaryResponse.netWork:type_name -> global.NetWorkInfo
	78,  // 8: global.PluginInfo.description:type_name -> global.PluginInfo.DescriptionEntry
	82,  // 9: global.SysInfoData.startTime:type_name -> google.protobuf.Timestamp
	12,  // 10: global.SysInfoData.plugins:type_name -> global.PluginInfo
	13,  // 11: global.SysInfoResponse.data:type_name -> global.SysInfoData
	82,  // 12: global.TaskTreeData.startTime:type_name -> google.protobuf.Timestamp
	79,  // 13: global.TaskTreeData.description:type_name -> global.TaskTreeData.DescriptionEntry
	15,  // 14: global.TaskTreeData.children:type_name -> global.TaskTreeData
	15,  // 15: global.TaskTreeData.blocked:type_name -> global.TaskTreeData
	15,  // 16: global.TaskTreeResponse.data:type_name -> global.TaskTreeData
	22,  // 17: global.StreamListResponse.data:type_name -> global.StreamInfo
	80,  // 18: global.StreamWaitListResponse.list:type_name -> global.StreamWaitListResponse.ListEntry
	22,  // 19: global.StreamInfoResponse.data:type_name -> global.StreamInfo
	28,  // 20: global.StreamInfo.audioTrack:type_name -> global.AudioTrackInfo
	31,  // 21: global.StreamInfo.videoTrack:type_name -> global.VideoTrackInfo
	82,  // 22: global.StreamInfo.startTime:type_name -> google.protobuf.Timestamp
	83,  // 23: global.StreamInfo.bufferTime:type_name -> google.protobuf.Duration
	23,  // 24: global.StreamInfo.recording:type_name -> global.RecordingDetail
	83,  // 25: global.RecordingDetail.fragment:type_name -> google.protobuf.Duration
	82,  // 26: global.TrackSnapShot.writeTime:type_name -> google.protobuf.Timestamp
	24,  // 27: global.TrackSnapShot.wrap:type_name -> global.Wrap
	26,  // 28: global.MemoryBlockGroup.list:type_name -> global.MemoryBlock
	25,  // 29: global.TrackSnapShotData.ring:type_name -> global.TrackSnapShot
	81,  // 30: global.TrackSnapShotData.reader:type_name -> global.TrackSnapShotData.ReaderEntry
	27,  // 31: global.TrackSnapShotData.memory:type_name -> global.MemoryBlockGroup
	29,  // 32: global.TrackSnapShotResponse.data:type_name -> global.TrackSnapShotData
	82,  // 33: global.SubscriberSnapShot.startTime:type_name -> google.protobuf.Timestamp
	37,  // 34: global.SubscriberSnapShot.audioReader:type_name -> global.RingReaderSnapShot
	37,  // 35: global.SubscriberSnapShot.videoReader:type_name -> global.RingReaderSnapShot
	83,  // 36: global.SubscriberSnapShot.bufferTime:type_name -> google.protobuf.Duration
	38,  // 37: global.SubscribersResponse.data:type_name -> global.SubscriberSnapShot
	41,  // 38: global.PullProxyListResponse.data:type_name -> global.PullProxyInfo
	82,  // 39: global.PullProxyInfo.createTime:type_name -> google.protobuf.Timestamp
	82,  // 40: global.PullProxyInfo.updateTime:type_name -> google.protobuf.Timestamp
	83,  // 41: global.PullProxyInfo.recordFragment:type_name -> google.protobuf.Duration
	83,  // 42: global.PullProxyInfo.checkInterval:type_name -> google.protobuf.Duration
	83,  // 43: global.UpdatePullProxyRequest.recordFragment:type_name -> google.protobuf.Duration
	83,  // 44: global.UpdatePullProxyRequest.checkInterval:type_name -> google.protobuf.Duration
	82,  // 45: global.PushProxyInfo.createTime:type_name -> google.protobuf.Timestamp
	82,  // 46: global.PushProxyInfo.updateTime:type_name -> google.protobuf.Timestamp
	43,  // 47: global.PushProxyListResponse.data:type_name -> global.PushProxyInfo
	47,  // 48: global.StreamAliasListResponse.data:type_name -> global.StreamAlias
	82,  // 49: global.Recording.startTime:type_name -> google.protobuf.Timestamp
	51,  // 50: global.RecordingListResponse.data:type_name -> global.Recording
	82,  // 51: global.PushInfo.startTime:type_name -> google.protobuf.Timestamp
	53,  // 52: global.PushListResponse.data:type_name -> global.PushInfo
	56,  // 53: global.TransformListResponse.data:type_name -> global.Transform
	82,  // 54: global.RecordFile.startTime:type_name -> google.protobuf.Timestamp
	82,  // 55: global.RecordFile.endTime:type_name -> google.protobuf.Timestamp
	82,  // 56: global.EventRecordFile.startTime:type_name -> google.protobuf.Timestamp
	82,  // 57: global.EventRecordFile.endTime:type_name -> google.protobuf.Timestamp
	59,  // 58: global.RecordResponseList.data:type_name -> global.RecordFile
	60,  // 59: global.EventRecordResponseList.data:type_name -> global.EventRecordFile
	82,  // 60: global.Catalog.startTime:type_name -> google.protobuf.Timestamp
	82,  // 61: global.Catalog.endTime:type_name -> google.protobuf.Timestamp
	63,  // 62: global.ResponseCatalog.data:type_name -> global.Catalog
	59,  // 63: global.ResponseDelete.data:type_name -> global.RecordFile
	82,  // 64: global.AlarmInfo.createdAt:type_name -> google.protobuf.Timestamp
	82,  // 65: global.AlarmInfo.updatedAt:type_name -> google.protobuf.Timestamp
	68,  // 66: global.AlarmListResponse.data:type_name -> global.AlarmInfo
	82,  // 67: global.Step.startedAt:type_name -> google.protobuf.Timestamp
	82,  // 68: global.Step.completedAt:type_name -> google.protobuf.Timestamp
	71,  // 69: global.SubscriptionProgressData.steps:type_name -> global.Step
	72,  // 70: global.SubscriptionProgressResponse.data:type_name -> global.SubscriptionProgressData
	83,  // 71: global.GlobalPullRequest.delayCloseTimeout:type_name -> google.protobuf.Duration
	83,  // 72: global.GlobalPullRequest.publishTimeout:type_name -> google.protobuf.Duration
	83,  // 73: global.GlobalPullRequest.waitCloseTimeout:type_name -> google.protobuf.Duration
	83,  // 74: global.GlobalPullRequest.idleTimeout:type_name -> google.protobuf.Duration
	83,  // 75: global.GlobalPullRequest.pauseTimeout:type_name -> google.protobuf.Duration
	83,  // 76: global.GlobalPullRequest.bufferTime:type_name -> google.protobuf.Duration
	2,   // 77: global.Formily.PropertiesEntry.value:type_name -> global.Formily
	84,  // 78: global.Formily.ComponentPropsEntry.value:type_name -> google.protobuf.Any
	2,   // 79: global.FormilyResponse.PropertiesEntry.value:type_name -> global.Formily
	85,  // 80: global.api.SysInfo:input_type -> google.protobuf.Empty
	85,  // 81: global.api.DisabledPlugins:input_type -> google.protobuf.Empty
	85,  // 82: global.api.Summary:input_type -> google.protobuf.Empty
	33,  // 83: global.api.Shutdown:input_type -> global.RequestWithId
	33,  // 84: global.api.Restart:input_type -> global.RequestWithId
	85,  // 85: global.api.TaskTree:input_type -> google.protobuf.Empty
	34,  // 86: global.api.StopTask:input_type -> global.RequestWithId64
	34,  // 87: global.api.RestartTask:input_type -> global.RequestWithId64
	17,  // 88: global.api.StreamList:input_type -> global.StreamListRequest
	85,  // 89: global.api.WaitList:input_type -> google.protobuf.Empty
	20,  // 90: global.api.StreamInfo:input_type -> global.StreamSnapRequest
	20,  // 91: global.api.PauseStream:input_type -> global.StreamSnapRequest
	20,  // 92: global.api.ResumeStream:input_type -> global.StreamSnapRequest
	49,  // 93: global.api.SetStreamSpeed:input_type -> global.SetStreamSpeedRequest
	50,  // 94: global.api.SeekStream:input_type -> global.SeekStreamRequest
	36,  // 95: global.api.GetSubscribers:input_type -> global.SubscribersRequest
	20,  // 96: global.api.AudioTrackSnap:input_type -> global.StreamSnapRequest
	20,  // 97: global.api.VideoTrackSnap:input_type -> global.StreamSnapRequest
	35,  // 98: global.api.ChangeSubscribe:input_type -> global.ChangeSubscribeRequest
	85,  // 99: global.api.GetStreamAlias:input_type -> google.protobuf.Empty
	46,  // 100: global.api.SetStreamAlias:input_type -> global.SetStreamAliasRequest
	20,  // 101: global.api.StopPublish:input_type -> global.StreamSnapRequest
	33,  // 102: global.api.StopSubscribe:input_type -> global.RequestWithId
	85,  // 103: global.api.GetConfigFile:input_type -> google.protobuf.Empty
	7,   // 104: global.api.UpdateConfigFile:input_type -> global.UpdateConfigFileRequest
	1,   // 105: global.api.GetConfig:input_type -> global.GetConfigRequest
	1,   // 106: global.api.GetFormily:input_type -> global.GetConfigRequest
	85,  // 107: global.api.GetPullProxyList:input_type -> google.protobuf.Empty
	41,  // 108: global.api.AddPullProxy:input_type -> global.PullProxyInfo
	33,  // 109: global.api.RemovePullProxy:input_type -> global.RequestWithId
	42,  // 110: global.api.UpdatePullProxy:input_type -> global.UpdatePullProxyRequest
	85,  // 111: global.api.GetPushProxyList:input_type -> google.protobuf.Empty
	43,  // 112: global.api.AddPushProxy:input_type -> global.PushProxyInfo
	33,  // 113: global.api.RemovePushProxy:input_type -> global.RequestWithId
	44,  // 114: global.api.UpdatePushProxy:input_type -> global.UpdatePushProxyRequest
	85,  // 115: global.api.GetRecording:input_type -> google.protobuf.Empty
	85,  // 116: global.api.GetTransformList:input_type -> google.protobuf.Empty
	58,  // 117: global.api.GetRecordList:input_type -> global.ReqRecordList
	58,  // 118: global.api.GetEventRecordList:input_type -> global.ReqRecordList
	67,  // 119: global.api.GetRecordCatalog:input_type -> global.ReqRecordCatalog
	65,  // 120: global.api.DeleteRecord:input_type -> global.ReqRecordDelete
	69,  // 121: global.api.GetAlarmList:input_type -> global.AlarmListRequest
	20,  // 122: global.api.GetSubscriptionProgress:input_type -> global.StreamSnapRequest
	74,  // 123: global.api.StartPull:input_type -> global.GlobalPullRequest
	14,  // 124: global.api.SysInfo:output_type -> global.SysInfoResponse
	0,   // 125: global.api.DisabledPlugins:output_type -> global.DisabledPluginsResponse
	11,  // 126: global.api.Summary:output_type -> global.SummaryResponse
	32,  // 127: global.api.Shutdown:output_type -> global.SuccessResponse
	32,  // 128: global.api.Restart:output_type -> global.SuccessResponse
	16,  // 129: global.api.TaskTree:output_type -> global.TaskTreeResponse
	32,  // 130: global.api.StopTask:output_type -> global.SuccessResponse
	32,  // 131: global.api.RestartTask:output_type -> global.SuccessResponse
	18,  // 132: global.api.StreamList:output_type -> global.StreamListResponse
	19,  // 133: global.api.WaitList:output_type -> global.StreamWaitListResponse
	21,  // 134: global.api.StreamInfo:output_type -> global.StreamInfoResponse
	32,  // 135: global.api.PauseStream:output_type -> global.SuccessResponse
	32,  // 136: global.api.ResumeStream:output_type -> global.SuccessResponse
	32,  // 137: global.api.SetStreamSpeed:output_type -> global.SuccessResponse
	32,  // 138: global.api.SeekStream:output_type -> global.SuccessResponse
	39,  // 139: global.api.GetSubscribers:output_type -> global.SubscribersResponse
	30,  // 140: global.api.AudioTrackSnap:output_type -> global.TrackSnapShotResponse
	30,  // 141: global.api.VideoTrackSnap:output_type -> global.TrackSnapShotResponse
	32,  // 142: global.api.ChangeSubscribe:output_type -> global.SuccessResponse
	48,  // 143: global.api.GetStreamAlias:output_type -> global.StreamAliasListResponse
	32,  // 144: global.api.SetStreamAlias:output_type -> global.SuccessResponse
	32,  // 145: global.api.StopPublish:output_type -> global.SuccessResponse
	32,  // 146: global.api.StopSubscribe:output_type -> global.SuccessResponse
	5,   // 147: global.api.GetConfigFile:output_type -> global.GetConfigFileResponse
	32,  // 148: global.api.UpdateConfigFile:output_type -> global.SuccessResponse
	6,   // 149: global.api.GetConfig:output_type -> global.GetConfigResponse
	6,   // 150: global.api.GetFormily:output_type -> global.GetConfigResponse
	40,  // 151: global.api.GetPullProxyList:output_type -> global.PullProxyListResponse
	32,  // 152: global.api.AddPullProxy:output_type -> global.SuccessResponse
	32,  // 153: global.api.RemovePullProxy:output_type -> global.SuccessResponse
	32,  // 154: global.api.UpdatePullProxy:output_type -> global.SuccessResponse
	45,  // 155: global.api.GetPushProxyList:output_type -> global.PushProxyListResponse
	32,  // 156: global.api.AddPushProxy:output_type -> global.SuccessResponse
	32,  // 157: global.api.RemovePushProxy:output_type -> global.SuccessResponse
	32,  // 158: global.api.UpdatePushProxy:output_type -> global.SuccessResponse
	52,  // 159: global.api.GetRecording:output_type -> global.RecordingListResponse
	57,  // 160: global.api.GetTransformList:output_type -> global.TransformListResponse
	61,  // 161: global.api.GetRecordList:output_type -> global.RecordResponseList
	62,  // 162: global.api.GetEventRecordList:output_type -> global.EventRecordResponseList
	64,  // 163: global.api.GetRecordCatalog:output_type -> global.ResponseCatalog
	66,  // 164: global.api.DeleteRecord:output_type -> global.ResponseDelete
	70,  // 165: global.api.GetAlarmList:output_type -> global.AlarmListResponse
	73,  // 166: global.api.GetSubscriptionProgress:output_type -> global.SubscriptionProgressResponse
	32,  // 167: global.api.StartPull:output_type -> global.SuccessResponse
	124, // [124:168] is the sub-list for method output_type
	80,  // [80:124] is the sub-list for method input_type
	80,  // [80:80] is the sub-list for extension type_name
	80,  // [80:80] is the sub-list for extension extendee
	0,   // [0:80] is the sub-list for field type_name
}

func init() { file_global_proto_init() }
func file_global_proto_init() {
	if File_global_proto != nil {
		return
	}
	file_global_proto_msgTypes[42].OneofWrappers = []any{}
	file_global_proto_msgTypes[44].OneofWrappers = []any{}
	file_global_proto_msgTypes[74].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_global_proto_rawDesc), len(file_global_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   82,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_global_proto_goTypes,
		DependencyIndexes: file_global_proto_depIdxs,
		MessageInfos:      file_global_proto_msgTypes,
	}.Build()
	File_global_proto = out.File
	file_global_proto_goTypes = nil
	file_global_proto_depIdxs = nil
}
