// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: global.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Api_SysInfo_FullMethodName                 = "/global.api/SysInfo"
	Api_DisabledPlugins_FullMethodName         = "/global.api/DisabledPlugins"
	Api_Summary_FullMethodName                 = "/global.api/Summary"
	Api_Shutdown_FullMethodName                = "/global.api/Shutdown"
	Api_Restart_FullMethodName                 = "/global.api/Restart"
	Api_TaskTree_FullMethodName                = "/global.api/TaskTree"
	Api_StopTask_FullMethodName                = "/global.api/StopTask"
	Api_RestartTask_FullMethodName             = "/global.api/RestartTask"
	Api_StreamList_FullMethodName              = "/global.api/StreamList"
	Api_WaitList_FullMethodName                = "/global.api/WaitList"
	Api_StreamInfo_FullMethodName              = "/global.api/StreamInfo"
	Api_PauseStream_FullMethodName             = "/global.api/PauseStream"
	Api_ResumeStream_FullMethodName            = "/global.api/ResumeStream"
	Api_SetStreamSpeed_FullMethodName          = "/global.api/SetStreamSpeed"
	Api_SeekStream_FullMethodName              = "/global.api/SeekStream"
	Api_GetSubscribers_FullMethodName          = "/global.api/GetSubscribers"
	Api_AudioTrackSnap_FullMethodName          = "/global.api/AudioTrackSnap"
	Api_VideoTrackSnap_FullMethodName          = "/global.api/VideoTrackSnap"
	Api_ChangeSubscribe_FullMethodName         = "/global.api/ChangeSubscribe"
	Api_GetStreamAlias_FullMethodName          = "/global.api/GetStreamAlias"
	Api_SetStreamAlias_FullMethodName          = "/global.api/SetStreamAlias"
	Api_StopPublish_FullMethodName             = "/global.api/StopPublish"
	Api_StopSubscribe_FullMethodName           = "/global.api/StopSubscribe"
	Api_GetConfigFile_FullMethodName           = "/global.api/GetConfigFile"
	Api_UpdateConfigFile_FullMethodName        = "/global.api/UpdateConfigFile"
	Api_GetConfig_FullMethodName               = "/global.api/GetConfig"
	Api_GetFormily_FullMethodName              = "/global.api/GetFormily"
	Api_GetPullProxyList_FullMethodName        = "/global.api/GetPullProxyList"
	Api_AddPullProxy_FullMethodName            = "/global.api/AddPullProxy"
	Api_RemovePullProxy_FullMethodName         = "/global.api/RemovePullProxy"
	Api_UpdatePullProxy_FullMethodName         = "/global.api/UpdatePullProxy"
	Api_GetPushProxyList_FullMethodName        = "/global.api/GetPushProxyList"
	Api_AddPushProxy_FullMethodName            = "/global.api/AddPushProxy"
	Api_RemovePushProxy_FullMethodName         = "/global.api/RemovePushProxy"
	Api_UpdatePushProxy_FullMethodName         = "/global.api/UpdatePushProxy"
	Api_GetRecording_FullMethodName            = "/global.api/GetRecording"
	Api_GetTransformList_FullMethodName        = "/global.api/GetTransformList"
	Api_GetRecordList_FullMethodName           = "/global.api/GetRecordList"
	Api_GetEventRecordList_FullMethodName      = "/global.api/GetEventRecordList"
	Api_GetRecordCatalog_FullMethodName        = "/global.api/GetRecordCatalog"
	Api_DeleteRecord_FullMethodName            = "/global.api/DeleteRecord"
	Api_GetAlarmList_FullMethodName            = "/global.api/GetAlarmList"
	Api_GetSubscriptionProgress_FullMethodName = "/global.api/GetSubscriptionProgress"
	Api_StartPull_FullMethodName               = "/global.api/StartPull"
)

// ApiClient is the client API for Api service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApiClient interface {
	SysInfo(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SysInfoResponse, error)
	DisabledPlugins(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*DisabledPluginsResponse, error)
	Summary(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SummaryResponse, error)
	Shutdown(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error)
	Restart(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error)
	TaskTree(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TaskTreeResponse, error)
	StopTask(ctx context.Context, in *RequestWithId64, opts ...grpc.CallOption) (*SuccessResponse, error)
	RestartTask(ctx context.Context, in *RequestWithId64, opts ...grpc.CallOption) (*SuccessResponse, error)
	StreamList(ctx context.Context, in *StreamListRequest, opts ...grpc.CallOption) (*StreamListResponse, error)
	WaitList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*StreamWaitListResponse, error)
	StreamInfo(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*StreamInfoResponse, error)
	PauseStream(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	ResumeStream(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	SetStreamSpeed(ctx context.Context, in *SetStreamSpeedRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	SeekStream(ctx context.Context, in *SeekStreamRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	GetSubscribers(ctx context.Context, in *SubscribersRequest, opts ...grpc.CallOption) (*SubscribersResponse, error)
	AudioTrackSnap(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*TrackSnapShotResponse, error)
	VideoTrackSnap(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*TrackSnapShotResponse, error)
	ChangeSubscribe(ctx context.Context, in *ChangeSubscribeRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	GetStreamAlias(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*StreamAliasListResponse, error)
	SetStreamAlias(ctx context.Context, in *SetStreamAliasRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	StopPublish(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	StopSubscribe(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error)
	GetConfigFile(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetConfigFileResponse, error)
	UpdateConfigFile(ctx context.Context, in *UpdateConfigFileRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	GetConfig(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error)
	GetFormily(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error)
	GetPullProxyList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PullProxyListResponse, error)
	AddPullProxy(ctx context.Context, in *PullProxyInfo, opts ...grpc.CallOption) (*SuccessResponse, error)
	RemovePullProxy(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error)
	UpdatePullProxy(ctx context.Context, in *UpdatePullProxyRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	GetPushProxyList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PushProxyListResponse, error)
	AddPushProxy(ctx context.Context, in *PushProxyInfo, opts ...grpc.CallOption) (*SuccessResponse, error)
	RemovePushProxy(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error)
	UpdatePushProxy(ctx context.Context, in *UpdatePushProxyRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
	GetRecording(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*RecordingListResponse, error)
	GetTransformList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TransformListResponse, error)
	GetRecordList(ctx context.Context, in *ReqRecordList, opts ...grpc.CallOption) (*RecordResponseList, error)
	GetEventRecordList(ctx context.Context, in *ReqRecordList, opts ...grpc.CallOption) (*EventRecordResponseList, error)
	GetRecordCatalog(ctx context.Context, in *ReqRecordCatalog, opts ...grpc.CallOption) (*ResponseCatalog, error)
	DeleteRecord(ctx context.Context, in *ReqRecordDelete, opts ...grpc.CallOption) (*ResponseDelete, error)
	GetAlarmList(ctx context.Context, in *AlarmListRequest, opts ...grpc.CallOption) (*AlarmListResponse, error)
	GetSubscriptionProgress(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*SubscriptionProgressResponse, error)
	StartPull(ctx context.Context, in *GlobalPullRequest, opts ...grpc.CallOption) (*SuccessResponse, error)
}

type apiClient struct {
	cc grpc.ClientConnInterface
}

func NewApiClient(cc grpc.ClientConnInterface) ApiClient {
	return &apiClient{cc}
}

func (c *apiClient) SysInfo(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SysInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SysInfoResponse)
	err := c.cc.Invoke(ctx, Api_SysInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DisabledPlugins(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*DisabledPluginsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DisabledPluginsResponse)
	err := c.cc.Invoke(ctx, Api_DisabledPlugins_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) Summary(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SummaryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SummaryResponse)
	err := c.cc.Invoke(ctx, Api_Summary_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) Shutdown(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_Shutdown_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) Restart(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_Restart_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) TaskTree(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TaskTreeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskTreeResponse)
	err := c.cc.Invoke(ctx, Api_TaskTree_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StopTask(ctx context.Context, in *RequestWithId64, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_StopTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) RestartTask(ctx context.Context, in *RequestWithId64, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_RestartTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StreamList(ctx context.Context, in *StreamListRequest, opts ...grpc.CallOption) (*StreamListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StreamListResponse)
	err := c.cc.Invoke(ctx, Api_StreamList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) WaitList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*StreamWaitListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StreamWaitListResponse)
	err := c.cc.Invoke(ctx, Api_WaitList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StreamInfo(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*StreamInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StreamInfoResponse)
	err := c.cc.Invoke(ctx, Api_StreamInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) PauseStream(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_PauseStream_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) ResumeStream(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_ResumeStream_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SetStreamSpeed(ctx context.Context, in *SetStreamSpeedRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_SetStreamSpeed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SeekStream(ctx context.Context, in *SeekStreamRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_SeekStream_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetSubscribers(ctx context.Context, in *SubscribersRequest, opts ...grpc.CallOption) (*SubscribersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubscribersResponse)
	err := c.cc.Invoke(ctx, Api_GetSubscribers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AudioTrackSnap(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*TrackSnapShotResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TrackSnapShotResponse)
	err := c.cc.Invoke(ctx, Api_AudioTrackSnap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) VideoTrackSnap(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*TrackSnapShotResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TrackSnapShotResponse)
	err := c.cc.Invoke(ctx, Api_VideoTrackSnap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) ChangeSubscribe(ctx context.Context, in *ChangeSubscribeRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_ChangeSubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetStreamAlias(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*StreamAliasListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StreamAliasListResponse)
	err := c.cc.Invoke(ctx, Api_GetStreamAlias_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) SetStreamAlias(ctx context.Context, in *SetStreamAliasRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_SetStreamAlias_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StopPublish(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_StopPublish_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StopSubscribe(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_StopSubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetConfigFile(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetConfigFileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConfigFileResponse)
	err := c.cc.Invoke(ctx, Api_GetConfigFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdateConfigFile(ctx context.Context, in *UpdateConfigFileRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_UpdateConfigFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetConfig(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConfigResponse)
	err := c.cc.Invoke(ctx, Api_GetConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetFormily(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConfigResponse)
	err := c.cc.Invoke(ctx, Api_GetFormily_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetPullProxyList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PullProxyListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PullProxyListResponse)
	err := c.cc.Invoke(ctx, Api_GetPullProxyList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddPullProxy(ctx context.Context, in *PullProxyInfo, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_AddPullProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) RemovePullProxy(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_RemovePullProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdatePullProxy(ctx context.Context, in *UpdatePullProxyRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_UpdatePullProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetPushProxyList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PushProxyListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushProxyListResponse)
	err := c.cc.Invoke(ctx, Api_GetPushProxyList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) AddPushProxy(ctx context.Context, in *PushProxyInfo, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_AddPushProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) RemovePushProxy(ctx context.Context, in *RequestWithId, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_RemovePushProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) UpdatePushProxy(ctx context.Context, in *UpdatePushProxyRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_UpdatePushProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetRecording(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*RecordingListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RecordingListResponse)
	err := c.cc.Invoke(ctx, Api_GetRecording_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetTransformList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TransformListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransformListResponse)
	err := c.cc.Invoke(ctx, Api_GetTransformList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetRecordList(ctx context.Context, in *ReqRecordList, opts ...grpc.CallOption) (*RecordResponseList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RecordResponseList)
	err := c.cc.Invoke(ctx, Api_GetRecordList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetEventRecordList(ctx context.Context, in *ReqRecordList, opts ...grpc.CallOption) (*EventRecordResponseList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EventRecordResponseList)
	err := c.cc.Invoke(ctx, Api_GetEventRecordList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetRecordCatalog(ctx context.Context, in *ReqRecordCatalog, opts ...grpc.CallOption) (*ResponseCatalog, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResponseCatalog)
	err := c.cc.Invoke(ctx, Api_GetRecordCatalog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DeleteRecord(ctx context.Context, in *ReqRecordDelete, opts ...grpc.CallOption) (*ResponseDelete, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResponseDelete)
	err := c.cc.Invoke(ctx, Api_DeleteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetAlarmList(ctx context.Context, in *AlarmListRequest, opts ...grpc.CallOption) (*AlarmListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AlarmListResponse)
	err := c.cc.Invoke(ctx, Api_GetAlarmList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetSubscriptionProgress(ctx context.Context, in *StreamSnapRequest, opts ...grpc.CallOption) (*SubscriptionProgressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubscriptionProgressResponse)
	err := c.cc.Invoke(ctx, Api_GetSubscriptionProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StartPull(ctx context.Context, in *GlobalPullRequest, opts ...grpc.CallOption) (*SuccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuccessResponse)
	err := c.cc.Invoke(ctx, Api_StartPull_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiServer is the server API for Api service.
// All implementations must embed UnimplementedApiServer
// for forward compatibility.
type ApiServer interface {
	SysInfo(context.Context, *emptypb.Empty) (*SysInfoResponse, error)
	DisabledPlugins(context.Context, *emptypb.Empty) (*DisabledPluginsResponse, error)
	Summary(context.Context, *emptypb.Empty) (*SummaryResponse, error)
	Shutdown(context.Context, *RequestWithId) (*SuccessResponse, error)
	Restart(context.Context, *RequestWithId) (*SuccessResponse, error)
	TaskTree(context.Context, *emptypb.Empty) (*TaskTreeResponse, error)
	StopTask(context.Context, *RequestWithId64) (*SuccessResponse, error)
	RestartTask(context.Context, *RequestWithId64) (*SuccessResponse, error)
	StreamList(context.Context, *StreamListRequest) (*StreamListResponse, error)
	WaitList(context.Context, *emptypb.Empty) (*StreamWaitListResponse, error)
	StreamInfo(context.Context, *StreamSnapRequest) (*StreamInfoResponse, error)
	PauseStream(context.Context, *StreamSnapRequest) (*SuccessResponse, error)
	ResumeStream(context.Context, *StreamSnapRequest) (*SuccessResponse, error)
	SetStreamSpeed(context.Context, *SetStreamSpeedRequest) (*SuccessResponse, error)
	SeekStream(context.Context, *SeekStreamRequest) (*SuccessResponse, error)
	GetSubscribers(context.Context, *SubscribersRequest) (*SubscribersResponse, error)
	AudioTrackSnap(context.Context, *StreamSnapRequest) (*TrackSnapShotResponse, error)
	VideoTrackSnap(context.Context, *StreamSnapRequest) (*TrackSnapShotResponse, error)
	ChangeSubscribe(context.Context, *ChangeSubscribeRequest) (*SuccessResponse, error)
	GetStreamAlias(context.Context, *emptypb.Empty) (*StreamAliasListResponse, error)
	SetStreamAlias(context.Context, *SetStreamAliasRequest) (*SuccessResponse, error)
	StopPublish(context.Context, *StreamSnapRequest) (*SuccessResponse, error)
	StopSubscribe(context.Context, *RequestWithId) (*SuccessResponse, error)
	GetConfigFile(context.Context, *emptypb.Empty) (*GetConfigFileResponse, error)
	UpdateConfigFile(context.Context, *UpdateConfigFileRequest) (*SuccessResponse, error)
	GetConfig(context.Context, *GetConfigRequest) (*GetConfigResponse, error)
	GetFormily(context.Context, *GetConfigRequest) (*GetConfigResponse, error)
	GetPullProxyList(context.Context, *emptypb.Empty) (*PullProxyListResponse, error)
	AddPullProxy(context.Context, *PullProxyInfo) (*SuccessResponse, error)
	RemovePullProxy(context.Context, *RequestWithId) (*SuccessResponse, error)
	UpdatePullProxy(context.Context, *UpdatePullProxyRequest) (*SuccessResponse, error)
	GetPushProxyList(context.Context, *emptypb.Empty) (*PushProxyListResponse, error)
	AddPushProxy(context.Context, *PushProxyInfo) (*SuccessResponse, error)
	RemovePushProxy(context.Context, *RequestWithId) (*SuccessResponse, error)
	UpdatePushProxy(context.Context, *UpdatePushProxyRequest) (*SuccessResponse, error)
	GetRecording(context.Context, *emptypb.Empty) (*RecordingListResponse, error)
	GetTransformList(context.Context, *emptypb.Empty) (*TransformListResponse, error)
	GetRecordList(context.Context, *ReqRecordList) (*RecordResponseList, error)
	GetEventRecordList(context.Context, *ReqRecordList) (*EventRecordResponseList, error)
	GetRecordCatalog(context.Context, *ReqRecordCatalog) (*ResponseCatalog, error)
	DeleteRecord(context.Context, *ReqRecordDelete) (*ResponseDelete, error)
	GetAlarmList(context.Context, *AlarmListRequest) (*AlarmListResponse, error)
	GetSubscriptionProgress(context.Context, *StreamSnapRequest) (*SubscriptionProgressResponse, error)
	StartPull(context.Context, *GlobalPullRequest) (*SuccessResponse, error)
	mustEmbedUnimplementedApiServer()
}

// UnimplementedApiServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedApiServer struct{}

func (UnimplementedApiServer) SysInfo(context.Context, *emptypb.Empty) (*SysInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SysInfo not implemented")
}
func (UnimplementedApiServer) DisabledPlugins(context.Context, *emptypb.Empty) (*DisabledPluginsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisabledPlugins not implemented")
}
func (UnimplementedApiServer) Summary(context.Context, *emptypb.Empty) (*SummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Summary not implemented")
}
func (UnimplementedApiServer) Shutdown(context.Context, *RequestWithId) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Shutdown not implemented")
}
func (UnimplementedApiServer) Restart(context.Context, *RequestWithId) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Restart not implemented")
}
func (UnimplementedApiServer) TaskTree(context.Context, *emptypb.Empty) (*TaskTreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskTree not implemented")
}
func (UnimplementedApiServer) StopTask(context.Context, *RequestWithId64) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopTask not implemented")
}
func (UnimplementedApiServer) RestartTask(context.Context, *RequestWithId64) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestartTask not implemented")
}
func (UnimplementedApiServer) StreamList(context.Context, *StreamListRequest) (*StreamListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StreamList not implemented")
}
func (UnimplementedApiServer) WaitList(context.Context, *emptypb.Empty) (*StreamWaitListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WaitList not implemented")
}
func (UnimplementedApiServer) StreamInfo(context.Context, *StreamSnapRequest) (*StreamInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StreamInfo not implemented")
}
func (UnimplementedApiServer) PauseStream(context.Context, *StreamSnapRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseStream not implemented")
}
func (UnimplementedApiServer) ResumeStream(context.Context, *StreamSnapRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeStream not implemented")
}
func (UnimplementedApiServer) SetStreamSpeed(context.Context, *SetStreamSpeedRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetStreamSpeed not implemented")
}
func (UnimplementedApiServer) SeekStream(context.Context, *SeekStreamRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeekStream not implemented")
}
func (UnimplementedApiServer) GetSubscribers(context.Context, *SubscribersRequest) (*SubscribersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscribers not implemented")
}
func (UnimplementedApiServer) AudioTrackSnap(context.Context, *StreamSnapRequest) (*TrackSnapShotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AudioTrackSnap not implemented")
}
func (UnimplementedApiServer) VideoTrackSnap(context.Context, *StreamSnapRequest) (*TrackSnapShotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoTrackSnap not implemented")
}
func (UnimplementedApiServer) ChangeSubscribe(context.Context, *ChangeSubscribeRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeSubscribe not implemented")
}
func (UnimplementedApiServer) GetStreamAlias(context.Context, *emptypb.Empty) (*StreamAliasListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStreamAlias not implemented")
}
func (UnimplementedApiServer) SetStreamAlias(context.Context, *SetStreamAliasRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetStreamAlias not implemented")
}
func (UnimplementedApiServer) StopPublish(context.Context, *StreamSnapRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopPublish not implemented")
}
func (UnimplementedApiServer) StopSubscribe(context.Context, *RequestWithId) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopSubscribe not implemented")
}
func (UnimplementedApiServer) GetConfigFile(context.Context, *emptypb.Empty) (*GetConfigFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfigFile not implemented")
}
func (UnimplementedApiServer) UpdateConfigFile(context.Context, *UpdateConfigFileRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateConfigFile not implemented")
}
func (UnimplementedApiServer) GetConfig(context.Context, *GetConfigRequest) (*GetConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfig not implemented")
}
func (UnimplementedApiServer) GetFormily(context.Context, *GetConfigRequest) (*GetConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFormily not implemented")
}
func (UnimplementedApiServer) GetPullProxyList(context.Context, *emptypb.Empty) (*PullProxyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPullProxyList not implemented")
}
func (UnimplementedApiServer) AddPullProxy(context.Context, *PullProxyInfo) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPullProxy not implemented")
}
func (UnimplementedApiServer) RemovePullProxy(context.Context, *RequestWithId) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePullProxy not implemented")
}
func (UnimplementedApiServer) UpdatePullProxy(context.Context, *UpdatePullProxyRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePullProxy not implemented")
}
func (UnimplementedApiServer) GetPushProxyList(context.Context, *emptypb.Empty) (*PushProxyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPushProxyList not implemented")
}
func (UnimplementedApiServer) AddPushProxy(context.Context, *PushProxyInfo) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPushProxy not implemented")
}
func (UnimplementedApiServer) RemovePushProxy(context.Context, *RequestWithId) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePushProxy not implemented")
}
func (UnimplementedApiServer) UpdatePushProxy(context.Context, *UpdatePushProxyRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePushProxy not implemented")
}
func (UnimplementedApiServer) GetRecording(context.Context, *emptypb.Empty) (*RecordingListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecording not implemented")
}
func (UnimplementedApiServer) GetTransformList(context.Context, *emptypb.Empty) (*TransformListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransformList not implemented")
}
func (UnimplementedApiServer) GetRecordList(context.Context, *ReqRecordList) (*RecordResponseList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordList not implemented")
}
func (UnimplementedApiServer) GetEventRecordList(context.Context, *ReqRecordList) (*EventRecordResponseList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEventRecordList not implemented")
}
func (UnimplementedApiServer) GetRecordCatalog(context.Context, *ReqRecordCatalog) (*ResponseCatalog, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordCatalog not implemented")
}
func (UnimplementedApiServer) DeleteRecord(context.Context, *ReqRecordDelete) (*ResponseDelete, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRecord not implemented")
}
func (UnimplementedApiServer) GetAlarmList(context.Context, *AlarmListRequest) (*AlarmListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlarmList not implemented")
}
func (UnimplementedApiServer) GetSubscriptionProgress(context.Context, *StreamSnapRequest) (*SubscriptionProgressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscriptionProgress not implemented")
}
func (UnimplementedApiServer) StartPull(context.Context, *GlobalPullRequest) (*SuccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartPull not implemented")
}
func (UnimplementedApiServer) mustEmbedUnimplementedApiServer() {}
func (UnimplementedApiServer) testEmbeddedByValue()             {}

// UnsafeApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiServer will
// result in compilation errors.
type UnsafeApiServer interface {
	mustEmbedUnimplementedApiServer()
}

func RegisterApiServer(s grpc.ServiceRegistrar, srv ApiServer) {
	// If the following call pancis, it indicates UnimplementedApiServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Api_ServiceDesc, srv)
}

func _Api_SysInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SysInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SysInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SysInfo(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DisabledPlugins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DisabledPlugins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_DisabledPlugins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DisabledPlugins(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_Summary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).Summary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_Summary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).Summary(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_Shutdown_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestWithId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).Shutdown(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_Shutdown_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).Shutdown(ctx, req.(*RequestWithId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_Restart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestWithId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).Restart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_Restart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).Restart(ctx, req.(*RequestWithId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_TaskTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).TaskTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_TaskTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).TaskTree(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StopTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestWithId64)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StopTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StopTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StopTask(ctx, req.(*RequestWithId64))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_RestartTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestWithId64)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).RestartTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_RestartTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).RestartTask(ctx, req.(*RequestWithId64))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StreamList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StreamList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StreamList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StreamList(ctx, req.(*StreamListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_WaitList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).WaitList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_WaitList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).WaitList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StreamInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamSnapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StreamInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StreamInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StreamInfo(ctx, req.(*StreamSnapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_PauseStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamSnapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).PauseStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_PauseStream_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).PauseStream(ctx, req.(*StreamSnapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_ResumeStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamSnapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ResumeStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_ResumeStream_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ResumeStream(ctx, req.(*StreamSnapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SetStreamSpeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStreamSpeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SetStreamSpeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SetStreamSpeed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SetStreamSpeed(ctx, req.(*SetStreamSpeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SeekStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeekStreamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SeekStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SeekStream_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SeekStream(ctx, req.(*SeekStreamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetSubscribers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetSubscribers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetSubscribers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetSubscribers(ctx, req.(*SubscribersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AudioTrackSnap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamSnapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AudioTrackSnap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AudioTrackSnap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AudioTrackSnap(ctx, req.(*StreamSnapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_VideoTrackSnap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamSnapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).VideoTrackSnap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_VideoTrackSnap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).VideoTrackSnap(ctx, req.(*StreamSnapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_ChangeSubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeSubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ChangeSubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_ChangeSubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ChangeSubscribe(ctx, req.(*ChangeSubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetStreamAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetStreamAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetStreamAlias_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetStreamAlias(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_SetStreamAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStreamAliasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).SetStreamAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_SetStreamAlias_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).SetStreamAlias(ctx, req.(*SetStreamAliasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StopPublish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamSnapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StopPublish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StopPublish_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StopPublish(ctx, req.(*StreamSnapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StopSubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestWithId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StopSubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StopSubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StopSubscribe(ctx, req.(*RequestWithId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetConfigFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetConfigFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetConfigFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetConfigFile(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdateConfigFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConfigFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdateConfigFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdateConfigFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdateConfigFile(ctx, req.(*UpdateConfigFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetConfig(ctx, req.(*GetConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetFormily_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetFormily(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetFormily_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetFormily(ctx, req.(*GetConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetPullProxyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetPullProxyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetPullProxyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetPullProxyList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddPullProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PullProxyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddPullProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddPullProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddPullProxy(ctx, req.(*PullProxyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_RemovePullProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestWithId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).RemovePullProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_RemovePullProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).RemovePullProxy(ctx, req.(*RequestWithId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdatePullProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePullProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdatePullProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdatePullProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdatePullProxy(ctx, req.(*UpdatePullProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetPushProxyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetPushProxyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetPushProxyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetPushProxyList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_AddPushProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushProxyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).AddPushProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_AddPushProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).AddPushProxy(ctx, req.(*PushProxyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_RemovePushProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestWithId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).RemovePushProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_RemovePushProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).RemovePushProxy(ctx, req.(*RequestWithId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_UpdatePushProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePushProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).UpdatePushProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_UpdatePushProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).UpdatePushProxy(ctx, req.(*UpdatePushProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetRecording_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetRecording(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetRecording_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetRecording(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetTransformList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetTransformList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetTransformList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetTransformList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReqRecordList)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetRecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetRecordList(ctx, req.(*ReqRecordList))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetEventRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReqRecordList)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetEventRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetEventRecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetEventRecordList(ctx, req.(*ReqRecordList))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetRecordCatalog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReqRecordCatalog)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetRecordCatalog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetRecordCatalog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetRecordCatalog(ctx, req.(*ReqRecordCatalog))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DeleteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReqRecordDelete)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DeleteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_DeleteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DeleteRecord(ctx, req.(*ReqRecordDelete))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetAlarmList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AlarmListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetAlarmList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetAlarmList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetAlarmList(ctx, req.(*AlarmListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetSubscriptionProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamSnapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetSubscriptionProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GetSubscriptionProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetSubscriptionProgress(ctx, req.(*StreamSnapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StartPull_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GlobalPullRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).StartPull(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_StartPull_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).StartPull(ctx, req.(*GlobalPullRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Api_ServiceDesc is the grpc.ServiceDesc for Api service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Api_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "global.api",
	HandlerType: (*ApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SysInfo",
			Handler:    _Api_SysInfo_Handler,
		},
		{
			MethodName: "DisabledPlugins",
			Handler:    _Api_DisabledPlugins_Handler,
		},
		{
			MethodName: "Summary",
			Handler:    _Api_Summary_Handler,
		},
		{
			MethodName: "Shutdown",
			Handler:    _Api_Shutdown_Handler,
		},
		{
			MethodName: "Restart",
			Handler:    _Api_Restart_Handler,
		},
		{
			MethodName: "TaskTree",
			Handler:    _Api_TaskTree_Handler,
		},
		{
			MethodName: "StopTask",
			Handler:    _Api_StopTask_Handler,
		},
		{
			MethodName: "RestartTask",
			Handler:    _Api_RestartTask_Handler,
		},
		{
			MethodName: "StreamList",
			Handler:    _Api_StreamList_Handler,
		},
		{
			MethodName: "WaitList",
			Handler:    _Api_WaitList_Handler,
		},
		{
			MethodName: "StreamInfo",
			Handler:    _Api_StreamInfo_Handler,
		},
		{
			MethodName: "PauseStream",
			Handler:    _Api_PauseStream_Handler,
		},
		{
			MethodName: "ResumeStream",
			Handler:    _Api_ResumeStream_Handler,
		},
		{
			MethodName: "SetStreamSpeed",
			Handler:    _Api_SetStreamSpeed_Handler,
		},
		{
			MethodName: "SeekStream",
			Handler:    _Api_SeekStream_Handler,
		},
		{
			MethodName: "GetSubscribers",
			Handler:    _Api_GetSubscribers_Handler,
		},
		{
			MethodName: "AudioTrackSnap",
			Handler:    _Api_AudioTrackSnap_Handler,
		},
		{
			MethodName: "VideoTrackSnap",
			Handler:    _Api_VideoTrackSnap_Handler,
		},
		{
			MethodName: "ChangeSubscribe",
			Handler:    _Api_ChangeSubscribe_Handler,
		},
		{
			MethodName: "GetStreamAlias",
			Handler:    _Api_GetStreamAlias_Handler,
		},
		{
			MethodName: "SetStreamAlias",
			Handler:    _Api_SetStreamAlias_Handler,
		},
		{
			MethodName: "StopPublish",
			Handler:    _Api_StopPublish_Handler,
		},
		{
			MethodName: "StopSubscribe",
			Handler:    _Api_StopSubscribe_Handler,
		},
		{
			MethodName: "GetConfigFile",
			Handler:    _Api_GetConfigFile_Handler,
		},
		{
			MethodName: "UpdateConfigFile",
			Handler:    _Api_UpdateConfigFile_Handler,
		},
		{
			MethodName: "GetConfig",
			Handler:    _Api_GetConfig_Handler,
		},
		{
			MethodName: "GetFormily",
			Handler:    _Api_GetFormily_Handler,
		},
		{
			MethodName: "GetPullProxyList",
			Handler:    _Api_GetPullProxyList_Handler,
		},
		{
			MethodName: "AddPullProxy",
			Handler:    _Api_AddPullProxy_Handler,
		},
		{
			MethodName: "RemovePullProxy",
			Handler:    _Api_RemovePullProxy_Handler,
		},
		{
			MethodName: "UpdatePullProxy",
			Handler:    _Api_UpdatePullProxy_Handler,
		},
		{
			MethodName: "GetPushProxyList",
			Handler:    _Api_GetPushProxyList_Handler,
		},
		{
			MethodName: "AddPushProxy",
			Handler:    _Api_AddPushProxy_Handler,
		},
		{
			MethodName: "RemovePushProxy",
			Handler:    _Api_RemovePushProxy_Handler,
		},
		{
			MethodName: "UpdatePushProxy",
			Handler:    _Api_UpdatePushProxy_Handler,
		},
		{
			MethodName: "GetRecording",
			Handler:    _Api_GetRecording_Handler,
		},
		{
			MethodName: "GetTransformList",
			Handler:    _Api_GetTransformList_Handler,
		},
		{
			MethodName: "GetRecordList",
			Handler:    _Api_GetRecordList_Handler,
		},
		{
			MethodName: "GetEventRecordList",
			Handler:    _Api_GetEventRecordList_Handler,
		},
		{
			MethodName: "GetRecordCatalog",
			Handler:    _Api_GetRecordCatalog_Handler,
		},
		{
			MethodName: "DeleteRecord",
			Handler:    _Api_DeleteRecord_Handler,
		},
		{
			MethodName: "GetAlarmList",
			Handler:    _Api_GetAlarmList_Handler,
		},
		{
			MethodName: "GetSubscriptionProgress",
			Handler:    _Api_GetSubscriptionProgress_Handler,
		},
		{
			MethodName: "StartPull",
			Handler:    _Api_StartPull_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "global.proto",
}
